Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.33f1 (b2c853adf198) revision 11716691'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 16087 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.33f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/Hydra Games/Void Voyager
-logFile
Logs/AssetImportWorker0.log
-srvPort
52591
Successfully changed project path to: C:/Users/<USER>/Hydra Games/Void Voyager
C:/Users/<USER>/Hydra Games/Void Voyager
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16236] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3099211219 [EditorId] 3099211219 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [16236] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3099211219 [EditorId] 3099211219 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 9.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1 (b2c853adf198)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Hydra Games/Void Voyager/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2820)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56272
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006458 seconds.
- Loaded All Assemblies, in  0.242 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.179 seconds
Domain Reload Profiling: 420ms
	BeginReloadAssembly (76ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (94ms)
		LoadAssemblies (75ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (93ms)
			TypeCache.Refresh (92ms)
				TypeCache.ScanAssembly (82ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (179ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (139ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (97ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.507 seconds
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.516 seconds
Domain Reload Profiling: 1020ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (14ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (150ms)
			TypeCache.Refresh (122ms)
				TypeCache.ScanAssembly (110ms)
			ScanForSourceGeneratedMonoScriptInfo (20ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (516ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (271ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 6.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5799 Unused Serialized files (Serialized files now loaded: 0)
Unloading 102 unused Assets / (0.9 MB). Loaded Objects now: 6244.
Memory consumption went from 225.3 MB to 224.4 MB.
Total: 4.566400 ms (FindLiveObjects: 0.384200 ms CreateObjectMapping: 0.222700 ms MarkObjects: 3.480500 ms  DeleteObjects: 0.477800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 384789.918799 seconds.
  path: Assets/Scripts/Inventory/Items/ItemData.cs
  artifactKey: Guid(52d37c52629771a4ebf176d3c8a3a22b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Items/ItemData.cs using Guid(52d37c52629771a4ebf176d3c8a3a22b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'bf380df218052f466c96e6ad7a15843b') in 0.003341 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.345 seconds
Refreshing native plugins compatible for Editor in 4.35 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.995 seconds
Domain Reload Profiling: 1337ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (180ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (995ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (401ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (274ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6250.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.791900 ms (FindLiveObjects: 0.510200 ms CreateObjectMapping: 0.271400 ms MarkObjects: 3.437800 ms  DeleteObjects: 0.570500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.385 seconds
Refreshing native plugins compatible for Editor in 4.71 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.918 seconds
Domain Reload Profiling: 1301ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (918ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (356ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (237ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6255.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.618100 ms (FindLiveObjects: 0.396900 ms CreateObjectMapping: 0.342000 ms MarkObjects: 3.368300 ms  DeleteObjects: 0.509700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.333 seconds
Refreshing native plugins compatible for Editor in 3.70 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.982 seconds
Domain Reload Profiling: 1313ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (174ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (982ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (416ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6260.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 5.191600 ms (FindLiveObjects: 0.435600 ms CreateObjectMapping: 0.331500 ms MarkObjects: 3.768800 ms  DeleteObjects: 0.654400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.330 seconds
Refreshing native plugins compatible for Editor in 5.28 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.978 seconds
Domain Reload Profiling: 1305ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (407ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (272ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6265.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.827300 ms (FindLiveObjects: 0.439900 ms CreateObjectMapping: 0.217300 ms MarkObjects: 3.644500 ms  DeleteObjects: 0.524400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.418 seconds
Refreshing native plugins compatible for Editor in 4.59 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.949 seconds
Domain Reload Profiling: 1364ms
	BeginReloadAssembly (139ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (228ms)
		LoadAssemblies (255ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (40ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (343ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6270.
Memory consumption went from 198.8 MB to 198.0 MB.
Total: 4.272900 ms (FindLiveObjects: 0.385800 ms CreateObjectMapping: 0.220200 ms MarkObjects: 3.121200 ms  DeleteObjects: 0.544700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.330 seconds
Refreshing native plugins compatible for Editor in 3.40 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.961 seconds
Domain Reload Profiling: 1289ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (170ms)
		LoadAssemblies (210ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (400ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (267ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6275.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.805900 ms (FindLiveObjects: 0.334400 ms CreateObjectMapping: 0.272300 ms MarkObjects: 3.663100 ms  DeleteObjects: 0.535100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.337 seconds
Refreshing native plugins compatible for Editor in 3.43 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.057 seconds
Domain Reload Profiling: 1391ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1057ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (416ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (278ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6280.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.551400 ms (FindLiveObjects: 0.428300 ms CreateObjectMapping: 0.278000 ms MarkObjects: 3.310100 ms  DeleteObjects: 0.533700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.364 seconds
Refreshing native plugins compatible for Editor in 4.79 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.931 seconds
Domain Reload Profiling: 1292ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (198ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (931ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6285.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.530100 ms (FindLiveObjects: 0.420800 ms CreateObjectMapping: 0.275900 ms MarkObjects: 3.189300 ms  DeleteObjects: 0.642600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.330 seconds
Refreshing native plugins compatible for Editor in 3.61 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.961 seconds
Domain Reload Profiling: 1289ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (165ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (962ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (404ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (271ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6290.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.290200 ms (FindLiveObjects: 0.379500 ms CreateObjectMapping: 0.243900 ms MarkObjects: 3.128500 ms  DeleteObjects: 0.536900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.505 seconds
Refreshing native plugins compatible for Editor in 4.53 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 38.644 seconds
Domain Reload Profiling: 42145ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (3307ms)
		LoadAssemblies (3355ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (38644ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 32.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6295.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 2.957400 ms (FindLiveObjects: 0.317100 ms CreateObjectMapping: 0.161000 ms MarkObjects: 2.151400 ms  DeleteObjects: 0.326700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.498 seconds
Refreshing native plugins compatible for Editor in 4.61 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.765 seconds
Domain Reload Profiling: 2259ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (284ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (431ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 6.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6300.
Memory consumption went from 198.9 MB to 198.1 MB.
Total: 4.513300 ms (FindLiveObjects: 0.390400 ms CreateObjectMapping: 0.247300 ms MarkObjects: 3.323500 ms  DeleteObjects: 0.550900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.340 seconds
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.032 seconds
Domain Reload Profiling: 1369ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (176ms)
		LoadAssemblies (217ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1032ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (289ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 8.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6305.
Memory consumption went from 198.9 MB to 198.1 MB.
Total: 4.822600 ms (FindLiveObjects: 0.456500 ms CreateObjectMapping: 0.276900 ms MarkObjects: 3.397200 ms  DeleteObjects: 0.690500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.364 seconds
Refreshing native plugins compatible for Editor in 4.13 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.857 seconds
Domain Reload Profiling: 1219ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (202ms)
		LoadAssemblies (225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (858ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (345ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6310.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.081100 ms (FindLiveObjects: 0.352800 ms CreateObjectMapping: 0.181200 ms MarkObjects: 3.025300 ms  DeleteObjects: 0.520800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.344 seconds
Refreshing native plugins compatible for Editor in 3.31 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.947 seconds
Domain Reload Profiling: 1288ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (170ms)
		LoadAssemblies (208ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (272ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6315.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.825000 ms (FindLiveObjects: 0.414400 ms CreateObjectMapping: 0.421500 ms MarkObjects: 3.330600 ms  DeleteObjects: 0.653500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.341 seconds
Refreshing native plugins compatible for Editor in 3.87 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.948 seconds
Domain Reload Profiling: 1286ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (176ms)
		LoadAssemblies (216ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (948ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (408ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6320.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.798600 ms (FindLiveObjects: 0.383900 ms CreateObjectMapping: 0.257600 ms MarkObjects: 3.345300 ms  DeleteObjects: 0.810400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.358 seconds
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.819 seconds
Domain Reload Profiling: 1174ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (196ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (819ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (217ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6325.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.085300 ms (FindLiveObjects: 0.402800 ms CreateObjectMapping: 0.249500 ms MarkObjects: 2.927100 ms  DeleteObjects: 0.505000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.345 seconds
Refreshing native plugins compatible for Editor in 3.51 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.001 seconds
Domain Reload Profiling: 1342ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1001ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (415ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6330.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.967700 ms (FindLiveObjects: 0.606900 ms CreateObjectMapping: 0.272800 ms MarkObjects: 3.253700 ms  DeleteObjects: 0.833200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.366 seconds
Refreshing native plugins compatible for Editor in 3.48 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.893 seconds
Domain Reload Profiling: 1257ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (204ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (894ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (334ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 6.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6335.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.769700 ms (FindLiveObjects: 0.399100 ms CreateObjectMapping: 0.209600 ms MarkObjects: 3.010900 ms  DeleteObjects: 1.148700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.331 seconds
Refreshing native plugins compatible for Editor in 3.83 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.986 seconds
Domain Reload Profiling: 1315ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (986ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (395ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (262ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6340.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 5.068300 ms (FindLiveObjects: 0.418200 ms CreateObjectMapping: 0.344800 ms MarkObjects: 3.483100 ms  DeleteObjects: 0.820700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.329 seconds
Refreshing native plugins compatible for Editor in 3.52 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.011 seconds
Domain Reload Profiling: 1338ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (176ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1011ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (266ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6345.
Memory consumption went from 198.9 MB to 198.0 MB.
Total: 4.680600 ms (FindLiveObjects: 0.391000 ms CreateObjectMapping: 0.300500 ms MarkObjects: 3.425400 ms  DeleteObjects: 0.562400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.332 seconds
Refreshing native plugins compatible for Editor in 3.64 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.058 seconds
Domain Reload Profiling: 1388ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1058ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (420ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6350.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.173600 ms (FindLiveObjects: 0.366400 ms CreateObjectMapping: 0.236300 ms MarkObjects: 3.091300 ms  DeleteObjects: 0.478600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 3.65 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.838 seconds
Domain Reload Profiling: 1192ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (200ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (838ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6355.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.927900 ms (FindLiveObjects: 0.396000 ms CreateObjectMapping: 0.219700 ms MarkObjects: 3.729300 ms  DeleteObjects: 0.581800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.327 seconds
Refreshing native plugins compatible for Editor in 3.63 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 1344ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (166ms)
		LoadAssemblies (203ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1019ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (424ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6360.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 5.074100 ms (FindLiveObjects: 0.479900 ms CreateObjectMapping: 0.468000 ms MarkObjects: 3.534100 ms  DeleteObjects: 0.590800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.446 seconds
Refreshing native plugins compatible for Editor in 6.36 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.233 seconds
Domain Reload Profiling: 1676ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (228ms)
		LoadAssemblies (287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (24ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1233ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 8.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6365.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.866100 ms (FindLiveObjects: 0.453600 ms CreateObjectMapping: 0.266800 ms MarkObjects: 3.296400 ms  DeleteObjects: 0.848200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.372 seconds
Refreshing native plugins compatible for Editor in 3.71 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.791 seconds
Domain Reload Profiling: 1160ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (198ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (791ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (329ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 7.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6370.
Memory consumption went from 198.9 MB to 198.1 MB.
Total: 4.235800 ms (FindLiveObjects: 0.416600 ms CreateObjectMapping: 0.253100 ms MarkObjects: 3.072400 ms  DeleteObjects: 0.492600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.328 seconds
Refreshing native plugins compatible for Editor in 3.88 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.972 seconds
Domain Reload Profiling: 1298ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (214ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (972ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (417ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (278ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6375.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.586300 ms (FindLiveObjects: 0.456000 ms CreateObjectMapping: 0.227400 ms MarkObjects: 3.439300 ms  DeleteObjects: 0.462100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.333 seconds
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.972 seconds
Domain Reload Profiling: 1302ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (972ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (270ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6380.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.951900 ms (FindLiveObjects: 0.427800 ms CreateObjectMapping: 0.265000 ms MarkObjects: 3.523200 ms  DeleteObjects: 0.734300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.327 seconds
Refreshing native plugins compatible for Editor in 3.46 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 1281ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (272ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 7.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6385.
Memory consumption went from 199.0 MB to 198.2 MB.
Total: 4.797400 ms (FindLiveObjects: 0.393500 ms CreateObjectMapping: 0.223400 ms MarkObjects: 3.256000 ms  DeleteObjects: 0.923700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.347 seconds
Refreshing native plugins compatible for Editor in 4.18 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.981 seconds
Domain Reload Profiling: 1325ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (184ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (981ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (408ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (275ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6390.
Memory consumption went from 199.0 MB to 198.2 MB.
Total: 5.032100 ms (FindLiveObjects: 0.623100 ms CreateObjectMapping: 0.304700 ms MarkObjects: 3.540000 ms  DeleteObjects: 0.563100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.324 seconds
Refreshing native plugins compatible for Editor in 3.46 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.928 seconds
Domain Reload Profiling: 1249ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (169ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (928ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (402ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (268ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6395.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 5.207000 ms (FindLiveObjects: 0.657100 ms CreateObjectMapping: 0.377700 ms MarkObjects: 3.352500 ms  DeleteObjects: 0.818600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.330 seconds
Refreshing native plugins compatible for Editor in 3.14 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.939 seconds
Domain Reload Profiling: 1267ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (165ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (940ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (408ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (275ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6400.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.181400 ms (FindLiveObjects: 0.395500 ms CreateObjectMapping: 0.201600 ms MarkObjects: 3.007400 ms  DeleteObjects: 0.575800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.331 seconds
Refreshing native plugins compatible for Editor in 4.33 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.955 seconds
Domain Reload Profiling: 1283ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (955ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (414ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (279ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6405.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 5.078600 ms (FindLiveObjects: 0.428200 ms CreateObjectMapping: 0.358500 ms MarkObjects: 3.443000 ms  DeleteObjects: 0.847600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.364 seconds
Refreshing native plugins compatible for Editor in 3.69 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.913 seconds
Domain Reload Profiling: 1275ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (202ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (914ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (217ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 6.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6410.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.303400 ms (FindLiveObjects: 0.409600 ms CreateObjectMapping: 0.268200 ms MarkObjects: 3.077200 ms  DeleteObjects: 0.547400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.334 seconds
Refreshing native plugins compatible for Editor in 3.83 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.949 seconds
Domain Reload Profiling: 1277ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (949ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (419ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (277ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 8.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6415.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 5.103600 ms (FindLiveObjects: 0.458100 ms CreateObjectMapping: 0.304400 ms MarkObjects: 3.588200 ms  DeleteObjects: 0.752000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.381 seconds
Refreshing native plugins compatible for Editor in 3.35 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.951 seconds
Domain Reload Profiling: 1329ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (211ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (952ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 5.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6420.
Memory consumption went from 199.0 MB to 198.1 MB.
Total: 4.441000 ms (FindLiveObjects: 0.391000 ms CreateObjectMapping: 0.206700 ms MarkObjects: 3.291100 ms  DeleteObjects: 0.551000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.450 seconds
Refreshing native plugins compatible for Editor in 4.33 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.338 seconds
Domain Reload Profiling: 1785ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (221ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1338ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 8.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6425.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.627800 ms (FindLiveObjects: 0.437600 ms CreateObjectMapping: 0.276500 ms MarkObjects: 3.292800 ms  DeleteObjects: 0.619800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.342 seconds
Refreshing native plugins compatible for Editor in 3.26 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.961 seconds
Domain Reload Profiling: 1301ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (179ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (421ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 8.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6430.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.918600 ms (FindLiveObjects: 0.404800 ms CreateObjectMapping: 0.280200 ms MarkObjects: 3.496000 ms  DeleteObjects: 0.736700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.414 seconds
Refreshing native plugins compatible for Editor in 6.88 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.322 seconds
Domain Reload Profiling: 1733ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (213ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1322ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (521ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 7.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6435.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 5.124600 ms (FindLiveObjects: 0.387700 ms CreateObjectMapping: 0.277700 ms MarkObjects: 3.655700 ms  DeleteObjects: 0.802200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.383 seconds
Refreshing native plugins compatible for Editor in 4.40 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.933 seconds
Domain Reload Profiling: 1314ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (203ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (934ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (347ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (224ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 8.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6440.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.265500 ms (FindLiveObjects: 0.434700 ms CreateObjectMapping: 0.259300 ms MarkObjects: 3.063700 ms  DeleteObjects: 0.506900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.333 seconds
Refreshing native plugins compatible for Editor in 3.64 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.955 seconds
Domain Reload Profiling: 1286ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (17ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (956ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (420ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (282ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 9.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6445.
Memory consumption went from 199.0 MB to 198.2 MB.
Total: 4.643400 ms (FindLiveObjects: 0.479600 ms CreateObjectMapping: 0.232700 ms MarkObjects: 3.279100 ms  DeleteObjects: 0.651000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.644 seconds
Refreshing native plugins compatible for Editor in 12.01 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 40.953 seconds
Domain Reload Profiling: 46591ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (5265ms)
		LoadAssemblies (5298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (84ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (43ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (40953ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6450.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.694100 ms (FindLiveObjects: 0.279400 ms CreateObjectMapping: 0.134600 ms MarkObjects: 3.926800 ms  DeleteObjects: 0.352400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.290 seconds
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 39.556 seconds
Domain Reload Profiling: 43843ms
	BeginReloadAssembly (287ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (3895ms)
		LoadAssemblies (3981ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (23ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (39556ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (482ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (336ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 5.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6455.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 6.520200 ms (FindLiveObjects: 1.337200 ms CreateObjectMapping: 0.164800 ms MarkObjects: 4.588400 ms  DeleteObjects: 0.428500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.000 seconds
Refreshing native plugins compatible for Editor in 4.26 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 23.855 seconds
Domain Reload Profiling: 28853ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (4756ms)
		LoadAssemblies (4809ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (18ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (23855ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (64ms)
			ProcessInitializeOnLoadAttributes (275ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 9.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6460.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 3.926400 ms (FindLiveObjects: 0.452000 ms CreateObjectMapping: 0.339000 ms MarkObjects: 2.747600 ms  DeleteObjects: 0.386900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.359 seconds
Refreshing native plugins compatible for Editor in 4.14 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 1285ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (195ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (369ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6465.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 4.562100 ms (FindLiveObjects: 0.436700 ms CreateObjectMapping: 0.231900 ms MarkObjects: 3.316100 ms  DeleteObjects: 0.575600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.352 seconds
Refreshing native plugins compatible for Editor in 3.98 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.035 seconds
Domain Reload Profiling: 1385ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (187ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1036ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (456ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 8.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6470.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 5.628200 ms (FindLiveObjects: 0.474300 ms CreateObjectMapping: 0.262800 ms MarkObjects: 3.868600 ms  DeleteObjects: 1.021200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.357 seconds
Refreshing native plugins compatible for Editor in 5.57 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.052 seconds
Domain Reload Profiling: 1407ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (187ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1053ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (467ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (315ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 8.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6475.
Memory consumption went from 199.1 MB to 198.3 MB.
Total: 4.782800 ms (FindLiveObjects: 0.400000 ms CreateObjectMapping: 0.254000 ms MarkObjects: 3.436600 ms  DeleteObjects: 0.690400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.459 seconds
Refreshing native plugins compatible for Editor in 3.58 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.186 seconds
Domain Reload Profiling: 2643ms
	BeginReloadAssembly (152ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (2186ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (369ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (249ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 10.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6480.
Memory consumption went from 199.1 MB to 198.3 MB.
Total: 3.323500 ms (FindLiveObjects: 0.315600 ms CreateObjectMapping: 0.145600 ms MarkObjects: 2.555300 ms  DeleteObjects: 0.306200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.361 seconds
Refreshing native plugins compatible for Editor in 3.65 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.056 seconds
Domain Reload Profiling: 1414ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1056ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6485.
Memory consumption went from 199.1 MB to 198.3 MB.
Total: 5.112600 ms (FindLiveObjects: 0.497000 ms CreateObjectMapping: 0.313800 ms MarkObjects: 3.443900 ms  DeleteObjects: 0.856700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.355 seconds
Refreshing native plugins compatible for Editor in 3.30 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 1289ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (418ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 6.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6490.
Memory consumption went from 199.2 MB to 198.3 MB.
Total: 5.054000 ms (FindLiveObjects: 0.500500 ms CreateObjectMapping: 0.276300 ms MarkObjects: 3.587500 ms  DeleteObjects: 0.688100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.486 seconds
Refreshing native plugins compatible for Editor in 7.15 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.156 seconds
Domain Reload Profiling: 1634ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (228ms)
		LoadAssemblies (294ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (21ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1156ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (422ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 8.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6495.
Memory consumption went from 199.1 MB to 198.2 MB.
Total: 5.329700 ms (FindLiveObjects: 0.497500 ms CreateObjectMapping: 0.362600 ms MarkObjects: 3.579600 ms  DeleteObjects: 0.889000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.458 seconds
Refreshing native plugins compatible for Editor in 3.67 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 1384ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (239ms)
		LoadAssemblies (280ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (348ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (235ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 6.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6500.
Memory consumption went from 199.2 MB to 198.3 MB.
Total: 4.238700 ms (FindLiveObjects: 0.547500 ms CreateObjectMapping: 0.231600 ms MarkObjects: 2.947200 ms  DeleteObjects: 0.510900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.333 seconds
Refreshing native plugins compatible for Editor in 3.43 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.956 seconds
Domain Reload Profiling: 1288ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (167ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (957ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (435ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 9.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6505.
Memory consumption went from 199.2 MB to 198.3 MB.
Total: 5.785100 ms (FindLiveObjects: 0.809400 ms CreateObjectMapping: 0.392000 ms MarkObjects: 3.821200 ms  DeleteObjects: 0.760800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.425 seconds
Refreshing native plugins compatible for Editor in 7.53 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.261 seconds
Domain Reload Profiling: 1683ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (217ms)
		LoadAssemblies (264ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1261ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (455ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5646 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6510.
Memory consumption went from 199.2 MB to 198.3 MB.
Total: 5.011300 ms (FindLiveObjects: 0.508000 ms CreateObjectMapping: 0.362400 ms MarkObjects: 3.495800 ms  DeleteObjects: 0.643600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
