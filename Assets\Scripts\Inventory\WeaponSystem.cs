using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages weapon functionality including reloading from inventory ammo
/// </summary>
public class WeaponSystem : MonoBehaviour
{
    [Header("References")]
    [Tooltip("Reference to the inventory manager")]
    public InventoryManager inventoryManager;

    [Toolt<PERSON>("Reference to the ammo counter UI")]
    public AmmoCounterUI ammoCounterUI;

    [<PERSON><PERSON>("Weapon Settings")]
    [Tooltip("Time it takes to reload a weapon")]
    public float reloadTime = 2f;

    private bool isReloading = false;

    private void Awake()
    {
        // Find inventory manager if not assigned
        if (inventoryManager == null)
        {
            inventoryManager = FindObjectOfType<InventoryManager>();
        }

        // Find ammo counter UI if not assigned
        if (ammoCounterUI == null)
        {
            ammoCounterUI = FindObjectOfType<AmmoCounterUI>();
        }
    }

    /// <summary>
    /// Attempt to reload a weapon from inventory ammo
    /// </summary>
    /// <param name="weapon">The weapon to reload</param>
    /// <returns>True if reload was successful</returns>
    public bool TryReloadWeapon(InventoryItem weapon)
    {
        if (weapon == null || inventoryManager == null) return false;
        if (weapon.itemData.itemType != ItemType.Weapon) return false;
        if (!weapon.CanReload()) return false;
        if (isReloading) return false;

        // Verify inventory consistency before looking for ammo
        inventoryManager.VerifyInventoryConsistency();

        // Find all compatible ammo in inventory
        List<InventoryItem> ammoItems = FindAllCompatibleAmmo(weapon.itemData.requiredAmmoType);
        if (ammoItems.Count == 0)
        {
            Debug.Log($"RELOAD FAILED: No {weapon.itemData.requiredAmmoType} ammo found for {weapon.GetName()}");
            return false;
        }

        // Start reload process with multiple ammo items
        StartCoroutine(ReloadWeaponCoroutine(weapon, ammoItems));
        return true;
    }

    /// <summary>
    /// Find all compatible ammo in inventory
    /// </summary>
    /// <param name="requiredAmmoType">The type of ammo needed</param>
    /// <returns>List of ammo items</returns>
    private List<InventoryItem> FindAllCompatibleAmmo(AmmoType requiredAmmoType)
    {
        // Always verify inventory consistency first to catch any tracking issues
        inventoryManager.VerifyInventoryConsistency();

        List<InventoryItem> ammoItems = new List<InventoryItem>();
        List<InventoryItem> allItems = inventoryManager.GetAllItems();

        Debug.Log($"AMMO SEARCH: Looking for {requiredAmmoType} ammo. Total tracked items: {allItems.Count}");

        foreach (InventoryItem item in allItems)
        {
            if (item != null && item.itemData != null)
            {
                Debug.Log($"  Checking item: {item.GetName()}, Type: {item.itemData.itemType}, AmmoType: {item.itemData.ammoType}, Stack: {item.stackCount}");

                if (item.itemData.itemType == ItemType.Ammo &&
                    item.itemData.ammoType == requiredAmmoType &&
                    item.stackCount > 0)
                {
                    ammoItems.Add(item);
                    Debug.Log($"  -> FOUND COMPATIBLE: {item.GetName()} with {item.stackCount} rounds");
                }
            }
            else
            {
                Debug.Log($"  Item is null or has null itemData");
            }
        }

        Debug.Log($"AMMO SEARCH RESULT: Found {ammoItems.Count} compatible ammo items");

        // If no ammo found through normal means, try alternative search
        if (ammoItems.Count == 0)
        {
            ammoItems = FindAmmoAlternativeMethod(requiredAmmoType);
        }

        return ammoItems;
    }



    /// <summary>
    /// Alternative method to find ammo by searching all InventoryItem objects in the scene
    /// This is a fallback in case items aren't properly tracked in the inventory manager
    /// </summary>
    private List<InventoryItem> FindAmmoAlternativeMethod(AmmoType requiredAmmoType)
    {
        List<InventoryItem> ammoItems = new List<InventoryItem>();

        // Find all InventoryItem objects that are children of the inventory manager
        InventoryItem[] allInventoryItems = inventoryManager.GetComponentsInChildren<InventoryItem>(true);

        foreach (InventoryItem item in allInventoryItems)
        {
            if (item == null || item.itemData == null) continue;

            // Only consider items that are actually in inventory (not active in world)
            if (item.gameObject.activeInHierarchy) continue;

            if (item.itemData.itemType == ItemType.Ammo &&
                item.itemData.ammoType == requiredAmmoType &&
                item.stackCount > 0)
            {
                ammoItems.Add(item);

                // Also add it back to the inventory manager's list if it's missing
                List<InventoryItem> currentItems = inventoryManager.GetAllItems();
                if (!currentItems.Contains(item))
                {
                    inventoryManager.AddItemToTracking(item);
                }
            }
        }

        // Sort by stack count (ascending) to use partial stacks first
        ammoItems.Sort((a, b) => a.stackCount.CompareTo(b.stackCount));

        // Force another consistency check after fixing tracking
        if (ammoItems.Count > 0)
        {
            inventoryManager.VerifyInventoryConsistency();
        }
        return ammoItems;
    }

    /// <summary>
    /// Coroutine to handle weapon reloading with timing using multiple ammo items
    /// </summary>
    private IEnumerator ReloadWeaponCoroutine(InventoryItem weapon, List<InventoryItem> ammoItems)
    {
        isReloading = true;

        Debug.Log($"Reloading {weapon.GetName()}...");

        // Wait for reload time
        yield return new WaitForSeconds(reloadTime);

        // Calculate how much ammo we need
        int ammoNeeded = weapon.itemData.magazineSize - weapon.currentAmmo;
        int totalAmmoLoaded = 0;
        List<InventoryItem> itemsToRemove = new List<InventoryItem>();

        // Go through ammo items and consume them until magazine is full
        foreach (InventoryItem ammoItem in ammoItems)
        {
            if (totalAmmoLoaded >= ammoNeeded) break; // Magazine is full

            int remainingNeeded = ammoNeeded - totalAmmoLoaded;
            int availableInThisItem = ammoItem.stackCount;
            int toTakeFromThisItem = Mathf.Min(remainingNeeded, availableInThisItem);

            // Remove ammo from this item
            ammoItem.RemoveFromStack(toTakeFromThisItem);
            totalAmmoLoaded += toTakeFromThisItem;

            // Mark for removal if empty
            if (ammoItem.stackCount <= 0)
            {
                itemsToRemove.Add(ammoItem);
            }
        }

        // Actually load the ammo into the weapon
        weapon.currentAmmo += totalAmmoLoaded;

        // Remove empty ammo items from inventory
        foreach (InventoryItem itemToRemove in itemsToRemove)
        {
            inventoryManager.RemoveItem(itemToRemove);
            Destroy(itemToRemove.gameObject);
        }

        // Check inventory state after removal and fix any tracking issues
        List<InventoryItem> remainingItems = inventoryManager.GetAllItems();

        // Also check what's actually in the inventory as children
        InventoryItem[] actualChildren = inventoryManager.GetComponentsInChildren<InventoryItem>(true);

        // Fix any tracking discrepancies
        foreach (InventoryItem child in actualChildren)
        {
            if (child != null && child.itemData != null && !child.gameObject.activeInHierarchy)
            {
                if (!remainingItems.Contains(child))
                {
                    inventoryManager.AddItemToTracking(child);
                }
            }
        }

        // Force another consistency check
        inventoryManager.VerifyInventoryConsistency();

        // Notify inventory of changes
        NotifyInventoryChanged();

        // Update hotbar displays
        UpdateHotbarDisplays();

        // Update ammo counter UI
        if (ammoCounterUI != null)
        {
            ammoCounterUI.ForceUpdate();
        }



        isReloading = false;
    }

    /// <summary>
    /// Fire a weapon (called by shooting system)
    /// </summary>
    /// <param name="weapon">The weapon to fire</param>
    /// <returns>True if weapon was fired successfully</returns>
    public bool FireWeapon(InventoryItem weapon)
    {
        if (weapon == null) return false;
        if (weapon.itemData.itemType != ItemType.Weapon) return false;
        if (weapon.currentAmmo <= 0) return false;

        // Consume ammo
        bool fired = weapon.Fire();
        if (fired)
        {
            // Update hotbar displays after firing
            UpdateHotbarDisplays();

            // Update ammo counter UI
            if (ammoCounterUI != null)
            {
                ammoCounterUI.ForceUpdate();
            }
        }

        return fired;
    }

    /// <summary>
    /// Get weapon status string for UI display
    /// </summary>
    /// <param name="weapon">The weapon to get status for</param>
    /// <returns>Status string like "12/12" for ammo count</returns>
    public string GetWeaponStatus(InventoryItem weapon)
    {
        if (weapon == null || weapon.itemData.itemType != ItemType.Weapon)
            return "";

        return $"{weapon.currentAmmo}/{weapon.itemData.magazineSize}";
    }

    /// <summary>
    /// Check if currently reloading
    /// </summary>
    public bool IsReloading()
    {
        return isReloading;
    }

    /// <summary>
    /// Notify inventory manager of changes (helper method to avoid event access issues)
    /// </summary>
    private void NotifyInventoryChanged()
    {
        if (inventoryManager != null)
        {
            // Use reflection to invoke the event or add a public method to InventoryManager
            // For now, we'll add a public method to InventoryManager
            inventoryManager.TriggerInventoryChanged();
        }
    }

    /// <summary>
    /// Update hotbar displays after weapon/ammo changes
    /// </summary>
    private void UpdateHotbarDisplays()
    {
        HotbarSlot[] hotbarSlots = FindObjectsOfType<HotbarSlot>();

        foreach (HotbarSlot slot in hotbarSlots)
        {
            if (slot.storedItem != null)
            {
                // Force update the visual by calling SetItem again
                var item = slot.storedItem;
                slot.SetItem(item);
            }
        }
    }
}
