using UnityEngine;

/// <summary>
/// Bullet projectile that travels in a direction and handles hit detection
/// </summary>
public class Bullet : MonoBehaviour
{
    [Header("Bullet Settings")]
    [Tooltip("Speed of the bullet")]
    public float speed = 50f;
    
    [Tooltip("Maximum distance the bullet can travel")]
    public float maxDistance = 100f;
    
    [Tooltip("Damage dealt by this bullet")]
    public float damage = 25f;
    
    [Tooltip("Layers that the bullet can hit")]
    public LayerMask hitLayers = -1;
    
    [Header("Visual Effects")]
    [Tooltip("Trail renderer for bullet trail")]
    public TrailRenderer trailRenderer;

    [Tooltip("Particle effect on impact")]
    public GameObject impactEffect;

    [Tooltip("Time before bullet is destroyed if it doesn't hit anything")]
    public float lifetime = 5f;

    [Header("Visual Settings")]
    [Tooltip("Scale multiplier for bullet visibility")]
    public float visualScale = 3f;
    
    // Private variables
    private Vector3 startPosition;
    private Vector3 direction;
    private float distanceTraveled = 0f;
    private bool hasHit = false;
    
    private void Start()
    {
        // Record starting position
        startPosition = transform.position;

        // Destroy bullet after lifetime
        Destroy(gameObject, lifetime);

        // Get trail renderer if not assigned
        if (trailRenderer == null)
            trailRenderer = GetComponent<TrailRenderer>();

        // Ensure trail renderer is properly configured
        if (trailRenderer != null)
        {
            trailRenderer.enabled = true;
            trailRenderer.emitting = true;
        }

        // Ensure bullet is properly scaled for visibility
        EnsureProperScale();
    }
    
    private void Update()
    {
        if (hasHit) return;

        // Move bullet
        MoveBullet();

        // Check for hits
        CheckForHits();

        // Check max distance
        CheckMaxDistance();
    }
    
    /// <summary>
    /// Initialize the bullet with direction and properties
    /// </summary>
    public void Initialize(Vector3 fireDirection, float bulletSpeed, float bulletDamage, float maxRange)
    {
        direction = fireDirection.normalized;
        speed = bulletSpeed;
        damage = bulletDamage;
        maxDistance = maxRange;

        // Point bullet in direction of travel
        transform.forward = direction;

        // Ensure trail renderer is set up immediately
        if (trailRenderer == null)
            trailRenderer = GetComponent<TrailRenderer>();

        if (trailRenderer != null)
        {
            trailRenderer.enabled = true;
            trailRenderer.emitting = true;
            trailRenderer.Clear(); // Clear any previous trail data
        }
    }

    /// <summary>
    /// Ensure bullet has proper scale for visibility
    /// </summary>
    private void EnsureProperScale()
    {
        // Apply visual scale to make bullet more visible
        if (visualScale > 0f)
        {
            transform.localScale = Vector3.one * visualScale;
        }

        // Ensure sprite renderer is properly configured
        SpriteRenderer spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer != null)
        {
            // Make sure the sprite is visible and properly sorted
            spriteRenderer.sortingOrder = 10; // Render above most other objects

            // Ensure the sprite has a bright, visible color
            if (spriteRenderer.color.a < 0.8f)
            {
                spriteRenderer.color = new Color(1f, 1f, 0.8f, 1f); // Bright yellow-white
            }
        }
    }
    
    /// <summary>
    /// Move the bullet forward
    /// </summary>
    private void MoveBullet()
    {
        float moveDistance = speed * Time.deltaTime;
        transform.position += direction * moveDistance;
        distanceTraveled += moveDistance;
    }
    
    /// <summary>
    /// Check for hits using raycast
    /// </summary>
    private void CheckForHits()
    {
        float checkDistance = speed * Time.deltaTime;
        
        // Raycast from current position in direction of movement
        if (Physics.Raycast(transform.position, direction, out RaycastHit hit, checkDistance, hitLayers))
        {
            OnHit(hit);
        }
    }
    
    /// <summary>
    /// Check if bullet has traveled maximum distance
    /// </summary>
    private void CheckMaxDistance()
    {
        if (distanceTraveled >= maxDistance)
        {
            OnMaxDistanceReached();
        }
    }
    
    /// <summary>
    /// Handle bullet hit
    /// </summary>
    private void OnHit(RaycastHit hit)
    {
        if (hasHit) return;
        hasHit = true;
        
        // Move bullet to exact hit point
        transform.position = hit.point;
        
        // Apply damage if target has health component
        IDamageable damageable = hit.collider.GetComponent<IDamageable>();
        if (damageable != null)
        {
            damageable.TakeDamage(damage);
            Debug.Log($"Bullet hit {hit.collider.name} for {damage} damage");
        }
        else
        {
            Debug.Log($"Bullet hit {hit.collider.name} (no damage component)");
        }
        
        // Spawn impact effect
        SpawnImpactEffect(hit.point, hit.normal);
        
        // Destroy bullet
        DestroyBullet();
    }
    
    /// <summary>
    /// Handle bullet reaching maximum distance
    /// </summary>
    private void OnMaxDistanceReached()
    {
        Debug.Log("Bullet reached maximum distance");
        DestroyBullet();
    }
    
    /// <summary>
    /// Spawn impact effect at hit location
    /// </summary>
    private void SpawnImpactEffect(Vector3 position, Vector3 normal)
    {
        if (impactEffect != null)
        {
            GameObject effect = Instantiate(impactEffect, position, Quaternion.LookRotation(normal));
            
            // Auto-destroy effect after a few seconds
            Destroy(effect, 3f);
        }
    }
    
    /// <summary>
    /// Destroy the bullet with cleanup
    /// </summary>
    private void DestroyBullet()
    {
        // Stop trail renderer from following
        if (trailRenderer != null)
        {
            trailRenderer.transform.SetParent(null);
            Destroy(trailRenderer.gameObject, trailRenderer.time);
        }
        
        // Destroy bullet
        Destroy(gameObject);
    }
    
    /// <summary>
    /// Visualize bullet path in scene view
    /// </summary>
    private void OnDrawGizmos()
    {
        if (Application.isPlaying && !hasHit)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawLine(transform.position, transform.position + direction * 2f);
        }
    }
}

/// <summary>
/// Interface for objects that can take damage
/// </summary>
public interface IDamageable
{
    void TakeDamage(float damage);
}
