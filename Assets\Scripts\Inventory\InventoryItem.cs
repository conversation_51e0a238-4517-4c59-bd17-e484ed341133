using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(SpriteRenderer))]
public class InventoryItem : MonoBehaviour
{
    public ItemData itemData;
    public Vector2Int position; // Add position here

    public bool isRotated = false;
    [HideInInspector] public bool wasInHotbar = false; // Track if item has been in hotbar

    [Header("Stack System")]
    [Tooltip("Current stack count for stackable items (ammo, consumables)")]
    public int stackCount = 30;

    [<PERSON><PERSON>("Weapon System")]
    [Tooltip("Current ammo in magazine (only for weapons)")]
    public int currentAmmo = 0;

    [Head<PERSON>("Drop Animation Settings")]
    [Tooltip("Duration of the drop animation in seconds")]
    public float dropAnimationDuration = 0.5f;
    [Tooltip("Animation curve for the drop movement")]
    public AnimationCurve dropAnimationCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    private SpriteRenderer spriteRenderer;

    private void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
    }

    private void Start()
    {
        if (itemData == null)
        {
            Debug.LogError("Missing ItemData on InventoryItem: " + gameObject.name);
            return;
        }

        spriteRenderer.sprite = itemData.worldSprite;
        UpdateVisualRotation();

        // Initialize item based on type
        InitializeItemData();

        // Force ammo to always be 30
        if (itemData.itemType == ItemType.Ammo)
        {
            stackCount = 30;
        }
    }

    /// <summary>
    /// Initialize item data based on item type
    /// </summary>
    private void InitializeItemData()
    {
        if (itemData == null) return;

        switch (itemData.itemType)
        {
            case ItemType.Weapon:
                // Weapons start with empty magazines
                if (currentAmmo == 0)
                {
                    currentAmmo = 0; // Explicitly set to 0 for new weapons
                }
                break;

            case ItemType.Ammo:
                // Ammo always has 30 rounds
                stackCount = 30;
                break;
        }
    }

    // Reset item state when it's dropped in the world
    public void ResetItemState()
    {
        wasInHotbar = false;
        position = new Vector2Int(-1, -1); // Invalid position to force proper placement
    }

    // Get the size of the item accounting for rotation
    public Vector2Int GetSize(bool useRotated)
    {
        if (itemData == null)
        {
            Debug.LogError("itemData is null for " + gameObject.name);
            return Vector2Int.one; // fallback size
        }

        // If useRotated is true, return the rotated dimensions
        return useRotated ? new Vector2Int(itemData.size.y, itemData.size.x) : itemData.size;
    }

    // Shorthand to get if the item is rotated
    public bool IsRotated => isRotated;

    // Toggle rotation
    public void Rotate()
    {
        if (!itemData.canRotate) return;

        isRotated = !isRotated;
        Debug.Log($"Item {itemData.itemName} rotated. IsRotated: {isRotated}");
        UpdateVisualRotation();
    }

    // Directly set rotation state
    public void SetRotation(bool rotated)
    {
        if (!itemData.canRotate) return;

        if (isRotated != rotated)
        {
            isRotated = rotated;
            Debug.Log($"Item {itemData.itemName} rotation set to: {isRotated}");
            UpdateVisualRotation();
        }
    }

    // Reset to default rotation
    public void ResetRotation()
    {
        isRotated = false;
        UpdateVisualRotation();
    }

    // Apply visual rotation to the sprite renderer
    private void UpdateVisualRotation()
    {
        if (spriteRenderer == null) return;

        // Only rotate the visual representation, don't scale
        transform.rotation = isRotated ?
            Quaternion.Euler(0, 0, -90) :
            Quaternion.identity;
    }

    // Get the current size accounting for rotation
    public Vector2Int GetRotatedSize()
    {
        if (itemData == null) return Vector2Int.one;

        // Return the size based on rotation state
        return isRotated ?
            new Vector2Int(itemData.size.y, itemData.size.x) :
            itemData.size;
    }

    public string GetName()
    {
        return itemData != null ? itemData.itemName : "Unknown";
    }

    // Get display name with stack count for ammo
    public string GetDisplayName()
    {
        if (itemData == null) return "Unknown";

        if (itemData.itemType == ItemType.Ammo && stackCount > 1)
        {
            return $"{itemData.itemName} ({stackCount})";
        }

        return itemData.itemName;
    }

    // Check if this item can stack with another item
    public bool CanStackWith(InventoryItem other)
    {
        if (other == null || itemData == null || other.itemData == null) return false;
        if (itemData.itemType != ItemType.Ammo) return false;
        if (itemData != other.itemData) return false;

        return stackCount + other.stackCount <= itemData.maxStackSize;
    }

    // Add to stack, returns remaining amount that couldn't be added
    public int AddToStack(int amount)
    {
        if (itemData == null || itemData.itemType != ItemType.Ammo) return amount;

        int maxCanAdd = itemData.maxStackSize - stackCount;
        int actualAdd = Mathf.Min(amount, maxCanAdd);

        stackCount += actualAdd;
        return amount - actualAdd;
    }

    // Remove from stack, returns actual amount removed
    public int RemoveFromStack(int amount)
    {
        if (itemData == null || itemData.itemType != ItemType.Ammo) return 0;

        int actualRemove = Mathf.Min(amount, stackCount);
        stackCount -= actualRemove;

        return actualRemove;
    }

    // Check if weapon can be reloaded
    public bool CanReload()
    {
        if (itemData == null || itemData.itemType != ItemType.Weapon) return false;
        return currentAmmo < itemData.magazineSize;
    }

    // Reload weapon with ammo, returns amount of ammo consumed
    public int Reload(int availableAmmo)
    {
        if (itemData == null || itemData.itemType != ItemType.Weapon) return 0;

        int ammoNeeded = itemData.magazineSize - currentAmmo;
        int ammoToLoad = Mathf.Min(ammoNeeded, availableAmmo);

        currentAmmo += ammoToLoad;
        return ammoToLoad;
    }

    // Fire weapon, returns true if shot was fired
    public bool Fire()
    {
        if (itemData == null || itemData.itemType != ItemType.Weapon) return false;
        if (currentAmmo <= 0) return false;

        currentAmmo--;
        return true;
    }

    // Shader-based visual feedback for pickup
    private ShaderBasedPickupIndicator pickupIndicator;

    // Call this when picked up
    public void OnPickup()
    {
        // Ensure item is properly initialized when picked up
        EnsureProperInitialization();
        gameObject.SetActive(false);
    }

    /// <summary>
    /// Ensure the item is properly initialized, especially for items picked up without opening inventory
    /// </summary>
    private void EnsureProperInitialization()
    {
        if (itemData == null)
        {
            Debug.LogError($"Item {gameObject.name} has no itemData when being picked up!");
            return;
        }

        // Re-run initialization to ensure proper state
        InitializeItemData();

        Debug.Log($"EnsureProperInitialization: {GetName()} - Type: {itemData.itemType}, " +
                 $"StackCount: {(itemData.itemType == ItemType.Ammo ? stackCount.ToString() : "N/A")}, " +
                 $"CurrentAmmo: {(itemData.itemType == ItemType.Weapon ? $"{currentAmmo}/{itemData.magazineSize}" : "N/A")}");
    }

    // Show visual feedback that this item can be picked up
    public void ShowPickupIndicator()
    {
        // Get or create the shader-based pickup indicator component
        if (pickupIndicator == null)
        {
            pickupIndicator = GetComponent<ShaderBasedPickupIndicator>();
            if (pickupIndicator == null)
            {
                pickupIndicator = gameObject.AddComponent<ShaderBasedPickupIndicator>();
            }
        }

        pickupIndicator.ShowIndicator();
    }

    // Hide visual feedback
    public void HidePickupIndicator()
    {
        if (pickupIndicator != null)
        {
            pickupIndicator.HideIndicator();
        }
    }

    // Check if this item is currently highlighted
    public bool IsHighlighted()
    {
        return pickupIndicator != null && pickupIndicator.IsHighlighted();
    }

    // Call this when dropped
    public void OnDrop(Vector3 dropPosition)
    {
        OnDrop(dropPosition, Vector3.zero);
    }

    // Call this when dropped with animation from player position
    public void OnDrop(Vector3 dropPosition, Vector3 playerPosition)
    {
        gameObject.SetActive(true);

        // If we have a valid player position, animate the drop
        if (playerPosition != Vector3.zero)
        {
            StartCoroutine(AnimateDropFromPlayer(playerPosition, dropPosition));
        }
        else
        {
            // Immediate drop without animation
            transform.position = dropPosition;
            ApplyRandomWorldRotation();
        }

        UpdateVisualRotation(); // Ensure inventory rotation is applied when dropped
        ResetItemState(); // Reset any item state tracking
    }

    // Animate the item dropping from player to drop position
    private System.Collections.IEnumerator AnimateDropFromPlayer(Vector3 startPosition, Vector3 endPosition)
    {
        // Set initial position at player
        transform.position = startPosition;

        // Use serialized animation settings
        float elapsedTime = 0f;

        // Store initial and target positions
        Vector3 initialPos = startPosition;
        Vector3 targetPos = endPosition;

        // Animate movement
        while (elapsedTime < dropAnimationDuration)
        {
            float t = elapsedTime / dropAnimationDuration;
            t = dropAnimationCurve.Evaluate(t);

            // Lerp position
            transform.position = Vector3.Lerp(initialPos, targetPos, t);

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        // Ensure final position is exact
        transform.position = targetPos;

        // Apply random rotation after animation completes
        ApplyRandomWorldRotation();
    }

    // Apply a random rotation to the world sprite (separate from inventory rotation)
    private void ApplyRandomWorldRotation()
    {
        // Generate random rotation angle (0-360 degrees)
        float randomAngle = Random.Range(0f, 360f);

        // Apply the random rotation to the transform
        // This is separate from the inventory rotation system
        Quaternion randomRotation = Quaternion.Euler(0, 0, randomAngle);

        // If the item was rotated in inventory, we need to combine rotations
        if (isRotated)
        {
            // Combine inventory rotation (-90 degrees) with random rotation
            Quaternion inventoryRotation = Quaternion.Euler(0, 0, -90);
            transform.rotation = randomRotation * inventoryRotation;
        }
        else
        {
            // Just apply random rotation
            transform.rotation = randomRotation;
        }
    }
}

