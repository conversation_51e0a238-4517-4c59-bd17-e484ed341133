{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18384, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18384, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18384, "tid": 1436, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18384, "tid": 1436, "ts": *********4518740, "dur": 720, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4523735, "dur": 857, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18384, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18384, "tid": 1, "ts": *********3057411, "dur": 4791, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": *********3062206, "dur": 26175, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": *********3088389, "dur": 58884, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4524595, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 18384, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18384, "tid": 12884901888, "ts": *********3055997, "dur": 2482, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3058481, "dur": 1452619, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3059321, "dur": 1738, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3061064, "dur": 1124, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3062191, "dur": 246, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3062439, "dur": 370, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3062811, "dur": 155, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3062968, "dur": 6, "ph": "X", "name": "ProcessMessages 14581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3062975, "dur": 537, "ph": "X", "name": "ReadAsync 14581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063513, "dur": 1, "ph": "X", "name": "ProcessMessages 2248", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063516, "dur": 159, "ph": "X", "name": "ReadAsync 2248", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063677, "dur": 5, "ph": "X", "name": "ProcessMessages 11212", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063683, "dur": 30, "ph": "X", "name": "ReadAsync 11212", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063715, "dur": 1, "ph": "X", "name": "ProcessMessages 2163", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063717, "dur": 18, "ph": "X", "name": "ReadAsync 2163", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063738, "dur": 17, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063758, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063792, "dur": 20, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063814, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063845, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063846, "dur": 17, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063866, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063889, "dur": 26, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063918, "dur": 19, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063939, "dur": 20, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063961, "dur": 20, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063984, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3063986, "dur": 26, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064017, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064019, "dur": 34, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064056, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064058, "dur": 42, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064103, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064126, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064151, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064184, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064186, "dur": 20, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064209, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064229, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064250, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064274, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064296, "dur": 52, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064351, "dur": 115, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064470, "dur": 2, "ph": "X", "name": "ProcessMessages 1278", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064473, "dur": 75, "ph": "X", "name": "ReadAsync 1278", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064550, "dur": 2, "ph": "X", "name": "ProcessMessages 3100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064553, "dur": 56, "ph": "X", "name": "ReadAsync 3100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064612, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064614, "dur": 27, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064644, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064645, "dur": 25, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064673, "dur": 19, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064695, "dur": 18, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064716, "dur": 18, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064736, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064755, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064778, "dur": 19, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064799, "dur": 18, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064820, "dur": 17, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064840, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064861, "dur": 25, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064889, "dur": 21, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064912, "dur": 34, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064950, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064978, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3064980, "dur": 22, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065005, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065035, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065036, "dur": 23, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065062, "dur": 20, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065085, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065107, "dur": 18, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065128, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065154, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065175, "dur": 54, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065231, "dur": 1, "ph": "X", "name": "ProcessMessages 1729", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065234, "dur": 228, "ph": "X", "name": "ReadAsync 1729", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065468, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065508, "dur": 1, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065511, "dur": 26, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065539, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065540, "dur": 49, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065592, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065625, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065656, "dur": 25, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065683, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065685, "dur": 20, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065707, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065708, "dur": 18, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065728, "dur": 55, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065785, "dur": 1, "ph": "X", "name": "ProcessMessages 1714", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065787, "dur": 19, "ph": "X", "name": "ReadAsync 1714", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065809, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065828, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065854, "dur": 20, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065877, "dur": 21, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065899, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065901, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065925, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065949, "dur": 20, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065971, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065972, "dur": 17, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3065992, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066013, "dur": 20, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066036, "dur": 19, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066057, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066080, "dur": 22, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066105, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066130, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066132, "dur": 20, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066155, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066177, "dur": 44, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066223, "dur": 26, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066252, "dur": 19, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066273, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066275, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066297, "dur": 20, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066319, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066321, "dur": 18, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066341, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066363, "dur": 23, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066389, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066411, "dur": 20, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066435, "dur": 17, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066456, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066483, "dur": 19, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066505, "dur": 26, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066533, "dur": 18, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066554, "dur": 20, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066576, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066600, "dur": 23, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066626, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066649, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066675, "dur": 19, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066697, "dur": 21, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066722, "dur": 25, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066751, "dur": 17, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066772, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066795, "dur": 18, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066816, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066841, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066864, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066889, "dur": 17, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066909, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066933, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066953, "dur": 19, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066975, "dur": 20, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3066998, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067023, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067024, "dur": 29, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067057, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067079, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067080, "dur": 23, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067105, "dur": 19, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067129, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067152, "dur": 20, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067174, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067196, "dur": 16, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067215, "dur": 23, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067241, "dur": 23, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067267, "dur": 18, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067288, "dur": 19, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067309, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067331, "dur": 23, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067356, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067357, "dur": 23, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067383, "dur": 18, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067405, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067427, "dur": 27, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067457, "dur": 24, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067484, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067507, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067529, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067551, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067553, "dur": 24, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067681, "dur": 41, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067723, "dur": 2, "ph": "X", "name": "ProcessMessages 3965", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067727, "dur": 28, "ph": "X", "name": "ReadAsync 3965", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067758, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067759, "dur": 70, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067831, "dur": 1, "ph": "X", "name": "ProcessMessages 1576", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067832, "dur": 18, "ph": "X", "name": "ReadAsync 1576", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067853, "dur": 32, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067888, "dur": 21, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067911, "dur": 18, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067931, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067953, "dur": 19, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3067976, "dur": 27, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068005, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068025, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068047, "dur": 21, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068072, "dur": 21, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068096, "dur": 22, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068120, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068143, "dur": 18, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068164, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068186, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068208, "dur": 25, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068236, "dur": 18, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068257, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068281, "dur": 19, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068303, "dur": 42, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068351, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068376, "dur": 19, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068398, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068421, "dur": 22, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068445, "dur": 20, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068468, "dur": 21, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068492, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068511, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068534, "dur": 18, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068555, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068577, "dur": 18, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068598, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068622, "dur": 20, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068645, "dur": 21, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068669, "dur": 20, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068691, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068712, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068732, "dur": 68, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068803, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068830, "dur": 21, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068854, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068875, "dur": 29, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068906, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068907, "dur": 27, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068936, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068958, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3068980, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069007, "dur": 19, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069028, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069051, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069073, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069093, "dur": 18, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069113, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069139, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069160, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069183, "dur": 60, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069245, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069267, "dur": 19, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069289, "dur": 46, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069337, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069380, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069404, "dur": 47, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069454, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069476, "dur": 94, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069573, "dur": 24, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069598, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069600, "dur": 34, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069636, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069658, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069679, "dur": 48, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069729, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069750, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069770, "dur": 19, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069791, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069811, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069850, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069876, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069896, "dur": 44, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069942, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069967, "dur": 18, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3069989, "dur": 17, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070008, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070050, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070088, "dur": 1, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070090, "dur": 40, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070132, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070153, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070173, "dur": 43, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070218, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070237, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070239, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070262, "dur": 15, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070280, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070314, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070343, "dur": 17, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070362, "dur": 36, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070400, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070421, "dur": 18, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070441, "dur": 43, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070487, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070508, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070529, "dur": 47, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070579, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070601, "dur": 17, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070620, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070638, "dur": 36, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070677, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070826, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070828, "dur": 33, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070862, "dur": 1, "ph": "X", "name": "ProcessMessages 1598", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070864, "dur": 48, "ph": "X", "name": "ReadAsync 1598", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070916, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070943, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070944, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3070966, "dur": 55, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071025, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071051, "dur": 18, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071072, "dur": 42, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071116, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071147, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071170, "dur": 44, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071216, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071237, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071257, "dur": 27, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071286, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071329, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071331, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071353, "dur": 23, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071379, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071400, "dur": 19, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071421, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071423, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071523, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071577, "dur": 9, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071588, "dur": 26, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071617, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071662, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071664, "dur": 18, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071684, "dur": 54, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071747, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071774, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071776, "dur": 19, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071798, "dur": 48, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071848, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071870, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071889, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071911, "dur": 19, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071932, "dur": 22, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071956, "dur": 18, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3071977, "dur": 103, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072084, "dur": 25, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072110, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072112, "dur": 16, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072130, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072158, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072160, "dur": 23, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072186, "dur": 41, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072230, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072254, "dur": 18, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072277, "dur": 39, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072318, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072346, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072369, "dur": 43, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072414, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072435, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072460, "dur": 17, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072479, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072520, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072541, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072563, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072586, "dur": 40, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072628, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072653, "dur": 18, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072674, "dur": 39, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072715, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072737, "dur": 25, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072765, "dur": 41, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072808, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072835, "dur": 19, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072856, "dur": 38, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072897, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072918, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072941, "dur": 43, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3072987, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073009, "dur": 18, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073030, "dur": 41, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073073, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073120, "dur": 1, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073121, "dur": 33, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073156, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073183, "dur": 20, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073206, "dur": 43, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073251, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073274, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073297, "dur": 20, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073320, "dur": 19, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073342, "dur": 17, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073361, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073382, "dur": 34, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073418, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073458, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073482, "dur": 20, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073504, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073506, "dur": 19, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073527, "dur": 16, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073546, "dur": 20, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073569, "dur": 17, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073588, "dur": 17, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073608, "dur": 35, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073646, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073669, "dur": 19, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073691, "dur": 40, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073733, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073755, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073776, "dur": 42, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073821, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073849, "dur": 16, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073868, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073889, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073930, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073950, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073973, "dur": 18, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3073993, "dur": 30, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074026, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074050, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074074, "dur": 37, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074114, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074139, "dur": 18, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074160, "dur": 38, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074200, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074223, "dur": 18, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074244, "dur": 17, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074264, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074305, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074332, "dur": 25, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074359, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074380, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074418, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074439, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074464, "dur": 16, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074483, "dur": 31, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074516, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074540, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074542, "dur": 20, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074565, "dur": 39, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074608, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074634, "dur": 18, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074655, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074677, "dur": 20, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074699, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074701, "dur": 17, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074722, "dur": 18, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074742, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074778, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074801, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074823, "dur": 40, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074865, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074888, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074909, "dur": 56, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074967, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3074989, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075012, "dur": 41, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075055, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075078, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075100, "dur": 42, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075146, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075168, "dur": 20, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075189, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075191, "dur": 32, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075227, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075252, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075253, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075273, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075298, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075336, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075360, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075383, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075384, "dur": 22, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075407, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075409, "dur": 18, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075429, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075451, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075473, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075514, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075535, "dur": 131, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075671, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3075746, "dur": 361, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076111, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076191, "dur": 4, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076196, "dur": 39, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076238, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076240, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076286, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076290, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076341, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076344, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076378, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076380, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076439, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076468, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076470, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076515, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076518, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076554, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076556, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076588, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076590, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076627, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076629, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076663, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076664, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076701, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076703, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076736, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076738, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076770, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076772, "dur": 38, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076812, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076814, "dur": 35, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076853, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076855, "dur": 17, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076873, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076875, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076918, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076920, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076963, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3076965, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077000, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077002, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077058, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077092, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077096, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077127, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077129, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077216, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077219, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077253, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077257, "dur": 33, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077292, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077295, "dur": 40, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077338, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077341, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077384, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077387, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077426, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077429, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077479, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077482, "dur": 64, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077550, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077554, "dur": 48, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077605, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077608, "dur": 35, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077646, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077648, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077719, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077721, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077758, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077761, "dur": 47, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077811, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077813, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077845, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077847, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077881, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077883, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077925, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3077930, "dur": 106, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078040, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078042, "dur": 81, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078128, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078131, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078173, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078176, "dur": 80, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078260, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078262, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078316, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078319, "dur": 28, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078349, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078352, "dur": 39, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078393, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078395, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078429, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078431, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078463, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078465, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078513, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078578, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3078620, "dur": 12232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3090859, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3090863, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3090901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3090903, "dur": 2625, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093533, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093536, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093592, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093640, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093807, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093839, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093912, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093946, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3093947, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3094065, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3094102, "dur": 5984, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100095, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100144, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100185, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100214, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100241, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100265, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100503, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100523, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100564, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100796, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100843, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100845, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100891, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100893, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100924, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100926, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3100989, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101018, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101056, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101083, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101111, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101239, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101268, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101269, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101299, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101398, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101425, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101605, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101631, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101756, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101782, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101808, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101871, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101899, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101927, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101956, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3101983, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102004, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102042, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102075, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102078, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102109, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102111, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102142, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102172, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102221, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102222, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102259, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102294, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102296, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102332, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102349, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102400, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102438, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102440, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102487, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102488, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102548, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102587, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102590, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102619, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102621, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102652, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102654, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102772, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102802, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102804, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102833, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102836, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102856, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102956, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3102986, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103045, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103067, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103069, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103097, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103116, "dur": 302, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103424, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103444, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3103445, "dur": 3176, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3106627, "dur": 469, "ph": "X", "name": "ProcessMessages 1524", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107099, "dur": 52, "ph": "X", "name": "ReadAsync 1524", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107154, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107155, "dur": 257, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107416, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107438, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3107439, "dur": 66082, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3173530, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3173534, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3173650, "dur": 2707, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3176362, "dur": 5657, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182028, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182032, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182079, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182082, "dur": 329, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182417, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182457, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182459, "dur": 299, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182762, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3182790, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183013, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183054, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183056, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183146, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183180, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183350, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183375, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183378, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183461, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183493, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183564, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183601, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183721, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183751, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183804, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183831, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183888, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3183918, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184001, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184027, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184063, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184090, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184438, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3184467, "dur": 717, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185187, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185215, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185435, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185463, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185668, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3185691, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186031, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186060, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186063, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186097, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186124, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186308, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186341, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186398, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186429, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186756, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186788, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186790, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3186821, "dur": 492, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187319, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187345, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187398, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187425, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187607, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187635, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187774, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187809, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187848, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3187877, "dur": 322, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188206, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188229, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188288, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188330, "dur": 415, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188751, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188785, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188786, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3188815, "dur": 288, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189109, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189142, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189257, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189294, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189296, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189344, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189377, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189379, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189413, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189598, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189634, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189666, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3189693, "dur": 319, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190016, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190049, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190075, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190102, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190128, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190163, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190192, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190228, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190259, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190293, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190325, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190352, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190366, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190393, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190420, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190452, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190454, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190477, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190502, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190511, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190542, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190570, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190597, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190623, "dur": 26, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190653, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190679, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190736, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190738, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190774, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190776, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190828, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190857, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190859, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190892, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190896, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190927, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190959, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190961, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3190982, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191015, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191017, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191040, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191069, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191070, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191103, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191105, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191136, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191138, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191171, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191173, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191204, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191206, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191244, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191246, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191277, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191280, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191302, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191304, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191341, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191343, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191386, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191389, "dur": 230, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191622, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191624, "dur": 186, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191813, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3191815, "dur": 215529, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3407353, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3407356, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3407379, "dur": 4179, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3411564, "dur": 65338, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3476915, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3476919, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3476960, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3476964, "dur": 173449, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3650422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3650425, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3650447, "dur": 28, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3650477, "dur": 279729, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3930216, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3930220, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3930287, "dur": 7, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********3930298, "dur": 71850, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4002155, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4002158, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4002179, "dur": 19, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4002199, "dur": 15408, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4017614, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4017617, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4017637, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4017640, "dur": 1455, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4019098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4019100, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4019127, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4019145, "dur": 25014, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4044167, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4044170, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4044191, "dur": 16, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4044209, "dur": 15276, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4059490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4059493, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4059510, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4059512, "dur": 699, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4060216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4060219, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4060251, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4060270, "dur": 13857, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4074134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4074137, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4074158, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4074160, "dur": 1256, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4075422, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4075471, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4075496, "dur": 416805, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4492311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4492314, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4492437, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4492440, "dur": 1095, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4493540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4493542, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4493601, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4493630, "dur": 785, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4494419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4494422, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4494457, "dur": 411, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": *********4494870, "dur": 16041, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4524606, "dur": 792, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18384, "tid": 8589934592, "ts": *********3053641, "dur": 93667, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": *********3147310, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": *********3147315, "dur": 1202, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4525405, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18384, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18384, "tid": 4294967296, "ts": *********3015501, "dur": 1496421, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": *********3026242, "dur": 6662, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": *********4511935, "dur": 4518, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": *********4514535, "dur": 77, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": *********4516505, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4525411, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": *********3056893, "dur": 2210, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********3059114, "dur": 1347, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********3060594, "dur": 64, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": *********3060659, "dur": 540, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********3061889, "dur": 1279, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": *********3063557, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": *********3064258, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 0, "ts": *********3061225, "dur": 15023, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********3076260, "dur": 1418021, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********4494282, "dur": 397, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********4494909, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********4494980, "dur": 1532, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": *********3061322, "dur": 14947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3076294, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3076430, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": *********3076383, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": *********3076864, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3076862, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": *********3077076, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077175, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077235, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077333, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077409, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077466, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077693, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3077757, "dur": 250, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": *********3078151, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3078667, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 1, "ts": *********3079098, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3079168, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3080344, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3082096, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3083490, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3084813, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3086609, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderUtils.cs"}}, {"pid": 12345, "tid": 1, "ts": *********3085837, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3087508, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3088602, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3089655, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3090398, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3091987, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3093435, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3094579, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3095878, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Machines\\IMachine.cs"}}, {"pid": 12345, "tid": 1, "ts": *********3094782, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3096584, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3098438, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3099690, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3100453, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3100890, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3101311, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": *********3101565, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3102410, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3102330, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": *********3103221, "dur": 1272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3104528, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": *********3104677, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3104735, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": *********3105077, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3105277, "dur": 75469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3182825, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3180747, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": *********3183040, "dur": 1025, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3184082, "dur": 1994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": *********3186077, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3188399, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3186168, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": *********3188611, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3190256, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3190988, "dur": 643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 1, "ts": *********3189002, "dur": 3029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": *********3192032, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": *********3192122, "dur": 1302329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3061466, "dur": 14833, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3076315, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3076386, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": *********3076307, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": *********3076452, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3076552, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3076551, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_F782C228CEDFF0CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": *********3076803, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3076927, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3076926, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": *********3076991, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3077593, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3077699, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": *********3077809, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3077966, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3078033, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": *********3078150, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3078342, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3078466, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3078539, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3078663, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": *********3079121, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3080419, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3081183, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3082910, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3083818, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3085378, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3086579, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3088294, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3089858, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3091139, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3092607, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugins\\ProjectSettingAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": *********3093243, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugins\\PluginSavedVersionMigration.cs"}}, {"pid": 12345, "tid": 2, "ts": *********3091907, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3093756, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3095736, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Events\\FrameDelayedCallback.cs"}}, {"pid": 12345, "tid": 2, "ts": *********3095301, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3096830, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3098014, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3099503, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3100447, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3100954, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3101308, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": *********3101632, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3101814, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3102343, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3102525, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3103031, "dur": 1424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3104463, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3104590, "dur": 2473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3107064, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": *********3107152, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3107519, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3107579, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3108196, "dur": 299742, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3412296, "dur": 64577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3412072, "dur": 64887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3477326, "dur": 88, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3477438, "dur": 173679, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": *********3654461, "dur": 87413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3741876, "dur": 186773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": *********3654460, "dur": 275263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": *********3930577, "dur": 151, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": *********3930901, "dur": 71906, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": *********4018131, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": *********4018130, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": *********4018324, "dur": 1499, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": *********4019829, "dur": 474471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3061518, "dur": 14991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3076510, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FA135A462A75797F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3076589, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3076748, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 3, "ts": *********3076747, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3076828, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3076883, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3076998, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": *********3076997, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3077280, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3077482, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3077712, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": *********3078005, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078066, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 3, "ts": *********3078137, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078200, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078252, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": *********3078349, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078425, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078518, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3078627, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": *********3078862, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3079015, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 3, "ts": *********3079071, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3079164, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3080944, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3083519, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.4\\Editor\\AsepriteImporter.cs"}}, {"pid": 12345, "tid": 3, "ts": *********3082605, "dur": 2464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3085069, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3086395, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3088375, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3090602, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\2D\\Shadows\\ShadowCaster2D.cs"}}, {"pid": 12345, "tid": 3, "ts": *********3089571, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3091304, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3093178, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3094732, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3095627, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3097084, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3098363, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3099981, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3100853, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3101302, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3101521, "dur": 1077, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3102971, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": *********3102604, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": *********3103196, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3103279, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3103681, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3104310, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3104375, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3104478, "dur": 1976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3106455, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": *********3106545, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": *********3106798, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3106880, "dur": 76332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3185857, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 3, "ts": *********3183214, "dur": 2744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": *********3185959, "dur": 777, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3186748, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": *********3189403, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3189499, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": *********3191891, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": *********3192121, "dur": 1302320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3061440, "dur": 14851, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3076308, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3076395, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": *********3076297, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3076698, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3076776, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3076775, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3076875, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3076986, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3076985, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3077262, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3077339, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3077402, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3077615, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3077737, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 4, "ts": *********3078031, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078109, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078164, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078435, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078516, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078584, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3078719, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 4, "ts": *********3079146, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3080963, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\TypeUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": *********3080580, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3083188, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\PsdBinaryReader.cs"}}, {"pid": 12345, "tid": 4, "ts": *********3082401, "dur": 2045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3084447, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3086622, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Processors\\MatrixNames.cs"}}, {"pid": 12345, "tid": 4, "ts": *********3085887, "dur": 2226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3088114, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3089251, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3090847, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3092532, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3094164, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3096265, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3097717, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3099196, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3099318, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3100422, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3100892, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3101500, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3101754, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3102122, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": *********3102632, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3102974, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3103027, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3103113, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3103321, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3103465, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3103519, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": *********3104047, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3104409, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3104485, "dur": 2018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3106504, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": *********3106603, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3106668, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": *********3107052, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3107228, "dur": 73531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3181191, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3180763, "dur": 3697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": *********3184460, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3184616, "dur": 2759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": *********3187375, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3187750, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3188157, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3188496, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3190742, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 4, "ts": *********3187468, "dur": 3708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": *********3191177, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3191245, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3191407, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3191703, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3191786, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3192045, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": *********3192389, "dur": 1302025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3061533, "dur": 14946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3076523, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3076480, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3076629, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3076687, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3076686, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F7F95DB68C78C481.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3076742, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077099, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077288, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077350, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3077348, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_CB8452BAE2BF35AA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3077418, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077574, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077704, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3077759, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": *********3078055, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": *********3078121, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078199, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078368, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078439, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078576, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078634, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": *********3078840, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3078896, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": *********3078951, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3079010, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": *********3079159, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3080836, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3082825, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\ImageResources\\UnicodeAlphaNames.cs"}}, {"pid": 12345, "tid": 5, "ts": *********3082472, "dur": 2153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3084625, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3086409, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3088684, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3090575, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Average.cs"}}, {"pid": 12345, "tid": 5, "ts": *********3090308, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3091906, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3094054, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\RenderGraph\\RenderGraphViewer.cs"}}, {"pid": 12345, "tid": 5, "ts": *********3094775, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\ProjectorEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": *********3093484, "dur": 2144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3095628, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3096692, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3097858, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3099397, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3100610, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3100854, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3101325, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3101521, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3101590, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3101800, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3102081, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3102186, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3101962, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": *********3102620, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3102784, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3102842, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3102994, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3103056, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": *********3103607, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3104409, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3104477, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3105821, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": *********3105917, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3105982, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": *********3106371, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3106518, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3106574, "dur": 74176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3180752, "dur": 2845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": *********3183598, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3183921, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3183734, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": *********3186632, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3186951, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3186823, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": *********3189280, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3189717, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3190102, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3190692, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3190758, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 5, "ts": *********3189465, "dur": 2747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": *********3192212, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": *********3192363, "dur": 1302059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3061565, "dur": 14936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3076523, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3076503, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3076639, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3076720, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3076719, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3076780, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3076964, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3077163, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3077163, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3077489, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3077758, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 6, "ts": *********3078052, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": *********3078490, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": *********3078542, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3078617, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": *********3079010, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17766336155681823506.rsp"}}, {"pid": 12345, "tid": 6, "ts": *********3079157, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3080967, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3082595, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3083420, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3084261, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3085370, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3087325, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3089009, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3090217, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3091832, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3093848, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3095589, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3096790, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3097649, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3099612, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3100271, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3100882, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3101290, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3101431, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3101712, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": *********3102446, "dur": 809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3103319, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3103785, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3103525, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": *********3104127, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3104280, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3104473, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": *********3104617, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3104671, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": *********3105100, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3105255, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3105315, "dur": 75439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3180883, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3181200, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3183730, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3180757, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": *********3184148, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3184908, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3184638, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": *********3187358, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3188156, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 6, "ts": *********3187484, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": *********3190199, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3190317, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191024, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191113, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191428, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191489, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191599, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191672, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3191867, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3192026, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********3192076, "dur": 867918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": *********4059996, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": *********4059996, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": *********4060153, "dur": 783, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": *********4060938, "dur": 433348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3061419, "dur": 14863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3076298, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3076428, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": *********3076385, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": *********3077071, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3077259, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3077472, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3077529, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": *********3077698, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3077754, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": *********3078072, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": *********3078139, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3078212, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3078581, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3078855, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3078966, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3079028, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 7, "ts": *********3079091, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3079156, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3080824, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3082017, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3084191, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3086531, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\MultiJsonInternal.cs"}}, {"pid": 12345, "tid": 7, "ts": *********3085696, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3088025, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3089789, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3090741, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3092003, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_6_to_1_7.cs"}}, {"pid": 12345, "tid": 7, "ts": *********3092003, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3094091, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3095715, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3097113, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3098349, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3099736, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3100330, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3100894, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3101324, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": *********3101533, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3102044, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3101633, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": *********3102453, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3102843, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": *********3103011, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3103555, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3103134, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": *********3103924, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3104613, "dur": 2781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3107394, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": *********3107518, "dur": 73255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3180827, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3182039, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Server.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3182734, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Quic.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3183248, "dur": 493, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3180775, "dur": 3655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": *********3184431, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3186729, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3186954, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3184524, "dur": 2901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": *********3187426, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3188570, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\netstandard.dll"}}, {"pid": 12345, "tid": 7, "ts": *********3187519, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": *********3189937, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3190121, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3190372, "dur": 1385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3191772, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3191849, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3191908, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": *********3192118, "dur": 1302287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3061487, "dur": 14901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3076473, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3076577, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3076575, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3076647, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3076748, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3076904, "dur": 416, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3076904, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6B34E6EF9E430549.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3077323, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3077479, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3077570, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3078018, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3078109, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3078248, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3078422, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3078760, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3078907, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079085, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079150, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079203, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079313, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079561, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079630, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079682, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079858, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3079973, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3080258, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3080471, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3080638, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3080921, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3081120, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3081384, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3081725, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3082035, "dur": 667, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3082720, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3082905, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3082968, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3083295, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3083828, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3084162, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3084345, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3084494, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3084699, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085013, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085251, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085422, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085479, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085622, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085816, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3085978, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3086094, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3086154, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3086310, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3086536, "dur": 608, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3087345, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3087539, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3087682, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3087821, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3088130, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3088188, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3088468, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3088749, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3088916, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089156, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089331, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089385, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089473, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089526, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089716, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3089902, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3090423, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3090648, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3090992, "dur": 371, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3091457, "dur": 619, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092078, "dur": 354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092480, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PostBuildCleanupAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092681, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092879, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092933, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3092989, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3093177, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Utils.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3093454, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 8, "ts": *********3077633, "dur": 16007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": *********3093641, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3094255, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3094364, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3094460, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3094687, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3094799, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3094941, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3095235, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3095581, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3095810, "dur": 387, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3096286, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3096574, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3096813, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3096922, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3097130, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3097240, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3097392, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3097635, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3097794, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3098024, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3098198, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3098569, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3098869, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3099019, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3099131, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3099667, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3099762, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3099822, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3100089, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3094520, "dur": 6205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": *********3100726, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3100881, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3100964, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": *********3101304, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3101454, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3101938, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3101528, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": *********3102519, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3102765, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3102841, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": *********3103004, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": *********3103471, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3104178, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": *********3104954, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3105796, "dur": 68484, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": *********3180736, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********3183003, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3183128, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********3185773, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3186949, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Razor.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3185907, "dur": 2080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********3187987, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3189679, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3188522, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********3191252, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3191485, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3191816, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3192000, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3192060, "dur": 220065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3412129, "dur": 516552, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": *********3412128, "dur": 517595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********3930814, "dur": 142, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": *********3930963, "dur": 113877, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": *********4059957, "dur": 433017, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": *********4059956, "dur": 433020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": *********4492994, "dur": 1187, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3061468, "dur": 14841, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3076328, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3076394, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": *********3076319, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3076614, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3076692, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3076691, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_7591B359D09ADD67.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3076754, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3076811, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3076810, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_C7611EE8A5C55055.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3076864, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3076956, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3077181, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3077179, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3077538, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": *********3077617, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3077838, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3077950, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3078031, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3078152, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3078498, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3078626, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": *********3079009, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 9, "ts": *********3079290, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3080552, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3081887, "dur": 2336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3084224, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3086421, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectOnEnumDescriptor.cs"}}, {"pid": 12345, "tid": 9, "ts": *********3085400, "dur": 2445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3087846, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3089188, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3090288, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3091584, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3092770, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Special\\ReflectedInspector.cs"}}, {"pid": 12345, "tid": 9, "ts": *********3092705, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3095533, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Serialization\\DictionaryAsset.cs"}}, {"pid": 12345, "tid": 9, "ts": *********3094633, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3096753, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3097848, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3099415, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3100188, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3100887, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3101295, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3101535, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": *********3102304, "dur": 1180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3103528, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": *********3103696, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3103793, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": *********3104357, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3104506, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3104741, "dur": 44507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3149249, "dur": 3623, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3152873, "dur": 27870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3181146, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3183920, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3180758, "dur": 3439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": *********3184198, "dur": 951, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3186955, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3185162, "dur": 2936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": *********3188099, "dur": 810, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3190758, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.DataContractSerialization.dll"}}, {"pid": 12345, "tid": 9, "ts": *********3188920, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": *********3191859, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3192035, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": *********3192099, "dur": 1302179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3061509, "dur": 14907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3076530, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3076596, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3076595, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3076754, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3076753, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3077024, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3077169, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3077168, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3077322, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3077388, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": *********3077459, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3077517, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": *********3077642, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3078153, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3078665, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11932646040550446581.rsp"}}, {"pid": 12345, "tid": 10, "ts": *********3079132, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3080636, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3082228, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3083442, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3085104, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3086638, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\EnumPropertyDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": *********3086638, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3088424, "dur": 2787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3091211, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3092184, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3093971, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\Volume\\VolumeGizmoDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": *********3093264, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3094712, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3095601, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsReflectedConverter.cs"}}, {"pid": 12345, "tid": 10, "ts": *********3095601, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3097526, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3098934, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3100085, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3100883, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3101292, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3101577, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3102399, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Ensure\\ExceptionMessages.cs"}}, {"pid": 12345, "tid": 10, "ts": *********3101552, "dur": 1718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": *********3103271, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3103372, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3103493, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3103788, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3104573, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\OnDisable.cs"}}, {"pid": 12345, "tid": 10, "ts": *********3103843, "dur": 1239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": *********3105082, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3105216, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": *********3105308, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": *********3105545, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3105642, "dur": 75121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3180764, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": *********3182862, "dur": 1575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3185457, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Session.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3184445, "dur": 2356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": *********3186802, "dur": 1685, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3188880, "dur": 726, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3189909, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-1-0.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3190335, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 10, "ts": *********3188495, "dur": 3037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": *********3191532, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3191716, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": *********3192250, "dur": 1302057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3061562, "dur": 14919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3076494, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3076483, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3076755, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3076754, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3076873, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3076961, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3076960, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D3933C87E9EDE72F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3077429, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3077527, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": *********3077623, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3077736, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": *********3078067, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": *********3078195, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3078605, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3078661, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": *********3078788, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3078871, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3079025, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 11, "ts": *********3079093, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3079169, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3080541, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3081737, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3083849, "dur": 2029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3086679, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\AssetPostProcessors\\ShaderGraphMaterialsUpdater.cs"}}, {"pid": 12345, "tid": 11, "ts": *********3085878, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3087449, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3088642, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3089687, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3091105, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3092706, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3094224, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3095518, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3097303, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3098669, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3099986, "dur": 860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3100850, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3100916, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3101321, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3102056, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3101780, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": *********3102579, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3102992, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3103184, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3103320, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3103430, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3103572, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": *********3104035, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3104196, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": *********3104344, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": *********3104747, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3105406, "dur": 75334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3181420, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3180742, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": *********3183086, "dur": 1084, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3184234, "dur": 554, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3186950, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3184180, "dur": 3055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": *********3187236, "dur": 1330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3190094, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 11, "ts": *********3188573, "dur": 2094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": *********3190667, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3190736, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3190792, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3191186, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3192009, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": *********3192078, "dur": 1302204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3061606, "dur": 14863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3076484, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3076470, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3076549, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3076681, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3076680, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3076804, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3076970, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3076969, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3077285, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3077358, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3077357, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3077414, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3077575, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3077755, "dur": 322, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": *********3078079, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3078432, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3078524, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3078594, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": *********3079115, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3080191, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3081847, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3082681, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3083979, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3085420, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3086391, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Collections\\DependencyCollection.cs"}}, {"pid": 12345, "tid": 12, "ts": *********3086284, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3088119, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3089206, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3091914, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Maximum.cs"}}, {"pid": 12345, "tid": 12, "ts": *********3090499, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3092678, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3093810, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3094719, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3095454, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3097478, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3098789, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3099983, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3100867, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3101305, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3101465, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3101616, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": *********3102190, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3102893, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3103014, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3103332, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3103565, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": *********3103701, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3103762, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": *********3104242, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3104384, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3104476, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": *********3104912, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3105213, "dur": 75535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3180749, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": *********3182588, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3182711, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": *********3184709, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3186349, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3184789, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": *********3187401, "dur": 712, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3188885, "dur": 1224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3190335, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3190702, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3190988, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 12, "ts": *********3188124, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": *********3191667, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3192036, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": *********3192170, "dur": 1302287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3061677, "dur": 14789, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3076485, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3076474, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": *********3076554, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3076710, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3076709, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": *********3076838, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3077059, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3077329, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3077452, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3077619, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3077755, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": *********3078145, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3078202, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3078594, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3078661, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": *********3079957, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Weapons\\Bullet.cs"}}, {"pid": 12345, "tid": 13, "ts": *********3079117, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3080491, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3081790, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3083234, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3084435, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3086578, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Options\\UnitOptionRow.cs"}}, {"pid": 12345, "tid": 13, "ts": *********3085325, "dur": 2312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3087638, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3089154, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3091034, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3093070, "dur": 1826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3094896, "dur": 1789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3096686, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3097867, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3099482, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3100512, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3100859, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3101314, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": *********3101505, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3101613, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": *********3102347, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3102780, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": *********3102919, "dur": 648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3103570, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": *********3104598, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3104769, "dur": 48147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3152920, "dur": 27859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3181041, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3182600, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3183205, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3180781, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": *********3183685, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3183868, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3184771, "dur": 1594, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3186987, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3188073, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": *********3183867, "dur": 4540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": *********3188407, "dur": 1941, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3190390, "dur": 1457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3191936, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": *********3192080, "dur": 1302209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3061715, "dur": 14736, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3076457, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3076452, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3076514, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3076595, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3076594, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0DA2B5FA22101DBE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3076696, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3076764, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3076763, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D0DC9EA7252E13B4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3076818, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3076909, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3076908, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0F9F7C5D1B98DB2C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3077020, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3077019, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3077074, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3077323, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3077394, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": *********3077734, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": *********3078011, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3078215, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3078540, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3078641, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3078693, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": *********3079069, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3079137, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3080864, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3083174, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Transitions\\FlowStateTransitionEditor.cs"}}, {"pid": 12345, "tid": 14, "ts": *********3082692, "dur": 2709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3086481, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\FlowDragAndDropUtility.cs"}}, {"pid": 12345, "tid": 14, "ts": *********3085402, "dur": 1892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3087959, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Util\\ShaderGraphRequirementsPerKeyword.cs"}}, {"pid": 12345, "tid": 14, "ts": *********3087295, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3088808, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3089957, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3091343, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3092944, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3094700, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3095585, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorExpandTooltipAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": *********3095484, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3096772, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3097912, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3099035, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3099913, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3099984, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3100850, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3100905, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3101318, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3101511, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3101585, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3102400, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\QueryVisualElementsExtensions.cs"}}, {"pid": 12345, "tid": 14, "ts": *********3101582, "dur": 1266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": *********3102849, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3102976, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3103160, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": *********3103671, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3103827, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3104181, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3104241, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3104474, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3104641, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3105408, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": *********3105646, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3105753, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": *********3105836, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749664*********, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3106225, "dur": 74508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": ****************, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.Routing.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3183205, "dur": 586, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 14, "ts": ****************, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3180747, "dur": 3794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": ****************, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3185011, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3186687, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3186989, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3184726, "dur": 3110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": *********3187837, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3189705, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3190335, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 14, "ts": *********3188032, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": *********3190861, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3191070, "dur": 902, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3191985, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3192038, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": *********3192352, "dur": 1302030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3061778, "dur": 14670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3076448, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": *********3076506, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3076626, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3076698, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3076697, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": *********3077034, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3077272, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3077440, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3077639, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3077701, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 15, "ts": *********3077954, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3078029, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": *********3078204, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3078493, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": *********3078620, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": *********3079065, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3079136, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3080073, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3081135, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3081691, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3082999, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3084163, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3086506, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\KeywordShaderStage.cs"}}, {"pid": 12345, "tid": 15, "ts": *********3086034, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3088168, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3089993, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Overrides\\ChromaticAberration.cs"}}, {"pid": 12345, "tid": 15, "ts": *********3089224, "dur": 2156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3091380, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3092669, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3093984, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3095269, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3096643, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3097823, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3099325, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3100163, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3100874, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3101303, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": *********3101475, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3101585, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": *********3102100, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3102629, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3102845, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3103068, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3103323, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3103381, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": *********3103554, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": *********3104414, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3104808, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": *********3104959, "dur": 1241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": *********3106201, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3106336, "dur": 76881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3183248, "dur": 631, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3184070, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3185710, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3185827, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3183218, "dur": 3638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": *********3186857, "dur": 1467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3188763, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3189591, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3190336, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Http.Json.dll"}}, {"pid": 12345, "tid": 15, "ts": *********3188332, "dur": 3264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": *********3191596, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": *********3192164, "dur": 1302163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3061810, "dur": 14634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3076445, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": *********3076500, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3076572, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 16, "ts": *********3076570, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": *********3076651, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3076722, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 16, "ts": *********3076721, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": *********3076849, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3077374, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3077515, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3077616, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3077753, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": *********3078482, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": *********3078667, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": *********3079037, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 16, "ts": *********3079090, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3079161, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3080612, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3081596, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3082874, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3083696, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3086640, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\OnEnterState.cs"}}, {"pid": 12345, "tid": 16, "ts": *********3084947, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3087226, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3089122, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3089767, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3091121, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3092067, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3093204, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3094114, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3095612, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3096685, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3098286, "dur": 1751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3100037, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3100849, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3101314, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": *********3101714, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3102258, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 16, "ts": *********3101957, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": *********3102660, "dur": 1569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3104236, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3104932, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": *********3105067, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": *********3105519, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3105614, "dur": 75125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3182005, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": *********3183730, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 16, "ts": ****************, "dur": 2294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 16, "ts": *********3180740, "dur": 5770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": *********3186511, "dur": 3458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3189981, "dur": 935, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3190963, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3191117, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3191950, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": *********3192126, "dur": 1302150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3061838, "dur": 14623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3076470, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3076462, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3076559, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3076640, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3077088, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3077086, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3077177, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3077461, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3077725, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": *********3078000, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078078, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 17, "ts": *********3078134, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078196, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078365, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078469, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078547, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3078698, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 17, "ts": *********3078947, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3079013, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12697856024727907050.rsp"}}, {"pid": 12345, "tid": 17, "ts": *********3079189, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3080895, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3083093, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3084968, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3086373, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3087830, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3089153, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3090355, "dur": 1812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3092168, "dur": 1915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3094083, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3095190, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnDragMessageListener.cs"}}, {"pid": 12345, "tid": 17, "ts": *********3094786, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3096608, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3097738, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3098570, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3100190, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3100884, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3101294, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3101449, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3101689, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3101938, "dur": 484, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3102636, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3101532, "dur": 1641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": *********3103174, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3103374, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3103505, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3103683, "dur": 1455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": *********3105139, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3105238, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3105326, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": *********3105719, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3105812, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3105897, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": *********3106195, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3106286, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": *********3106371, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": *********3106574, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3106727, "dur": 74018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3182214, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3180746, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": *********3182996, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3183921, "dur": 1020, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3183482, "dur": 2940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": *********3186423, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3188395, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3187020, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": *********3189430, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3190414, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordbi.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3190755, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Intrinsics.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3190988, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 17, "ts": *********3189506, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": *********3191846, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": *********3192127, "dur": 1302265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3061864, "dur": 14630, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3076526, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 18, "ts": *********3076508, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3076731, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3076873, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3077081, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 18, "ts": *********3077080, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3077417, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3077559, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": *********3077700, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": *********3078069, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 18, "ts": *********3078160, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3078253, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3078370, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3078482, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3078536, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3078636, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": *********3078833, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3079004, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 18, "ts": *********3079166, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3080438, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3081030, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3082502, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3083982, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3084616, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3086540, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Controls.cs"}}, {"pid": 12345, "tid": 18, "ts": *********3086097, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3087601, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3089066, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3090623, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3091536, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3093005, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3094617, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3096418, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3097425, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3098785, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3099806, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3100054, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3100871, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3101304, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3101503, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3101977, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": *********3102574, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3102678, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3102999, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3103380, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3103523, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3103765, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": *********3104210, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3104419, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3104480, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3104661, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3104738, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": *********3105195, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3105752, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3105878, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": *********3106352, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3106450, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": *********3106529, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": *********3106790, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3106865, "dur": 73865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3180732, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": *********3182936, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3183141, "dur": 2089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": *********3185231, "dur": 1525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3187660, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 18, "ts": *********3186761, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": *********3189250, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3190095, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 18, "ts": *********3190700, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 18, "ts": *********3189825, "dur": 2394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": *********3192220, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": *********3192357, "dur": 1301984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3061880, "dur": 14557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3076454, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3076438, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3076567, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3076727, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3076725, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_277964AD70D626E0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3076790, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3076859, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3076858, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3076967, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3076966, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3077020, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3077081, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3077079, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3077203, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3077202, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3077591, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3077711, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": *********3077883, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3077964, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3078053, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3078155, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3078359, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3078668, "dur": 398, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 19, "ts": *********3079119, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3080523, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3083591, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor@2c0153a9ba\\Editor\\Cli.cs"}}, {"pid": 12345, "tid": 19, "ts": *********3081898, "dur": 2445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3084343, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3086434, "dur": 709, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\ShaderGUI\\GenericShaderGraphMaterialGUI.cs"}}, {"pid": 12345, "tid": 19, "ts": *********3085511, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3087441, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3088129, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\CustomInterpolatorNode.cs"}}, {"pid": 12345, "tid": 19, "ts": *********3088090, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3089494, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3091103, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3092410, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3093660, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3095048, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3096295, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3097139, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3098171, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3099179, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3100096, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3100885, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3101300, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3101956, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3102014, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": *********3102591, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3102718, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3103003, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": *********3103172, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": *********3103670, "dur": 1050, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3104731, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3104790, "dur": 75946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3181413, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3182473, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3183862, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3184032, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3180748, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": *********3184175, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3184288, "dur": 1784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": *********3186072, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3186729, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3187075, "dur": 1103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3188495, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3188732, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Abstractions.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3188882, "dur": 722, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3190095, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3190414, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 19, "ts": *********3186381, "dur": 5150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": *********3191531, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3191629, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3192003, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": *********3192113, "dur": 1302171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3061914, "dur": 14593, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3076519, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3076510, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3076782, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3077089, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": *********3076780, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EC1D9C7602E10CB1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3077193, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3077398, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3077480, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3077569, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3077873, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3077937, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078019, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078249, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078478, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078550, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078767, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078924, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3078983, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079038, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079133, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079238, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079332, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079525, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079684, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079854, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3079958, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3080157, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3080499, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3080795, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3081048, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3081109, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3081212, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3081583, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3082093, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3082313, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3082786, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3083120, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3083728, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3084163, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3084289, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3084460, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3084600, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3084889, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3085189, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3085412, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3085733, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3085895, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3085966, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3086196, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3086352, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3086619, "dur": 514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3087133, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3087384, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3087526, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3087714, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3087834, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088028, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088160, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088260, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088627, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088836, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3088964, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3089082, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3089476, "dur": 621, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3090162, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3090366, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3090632, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3090793, "dur": 625, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3091419, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3077654, "dur": 13818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3091473, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3091578, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3092777, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\MetadataCollectionAdaptor.cs"}}, {"pid": 12345, "tid": 20, "ts": *********3092756, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3094691, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3095817, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3097147, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3098229, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3099574, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3100435, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3100887, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3101326, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3101665, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3101798, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3102349, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3103119, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3103175, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3103322, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3103554, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3103516, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3104148, "dur": 1511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3105708, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3105821, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3106391, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3106497, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3106575, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3106979, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3107124, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": *********3107313, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3107390, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": *********3107490, "dur": 73286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3180799, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3181066, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3182204, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-sysinfo-l1-1-0.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3183204, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3183863, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3184072, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3180777, "dur": 3664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": *********3184441, "dur": 2667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3187295, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 20, "ts": *********3187117, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": *********3189900, "dur": 838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3190885, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3191139, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3191223, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3191434, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********3192071, "dur": 826099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": *********4018172, "dur": 56524, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": *********4018171, "dur": 56526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": *********4074715, "dur": 1404, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": *********4076123, "dur": 418339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": *********4500725, "dur": 10484, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18384, "tid": 1436, "ts": *********4526203, "dur": 17454, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18384, "tid": 1436, "ts": *********4543736, "dur": 1972, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18384, "tid": 1436, "ts": *********4522342, "dur": 24233, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}