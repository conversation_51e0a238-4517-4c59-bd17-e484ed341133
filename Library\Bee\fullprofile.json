{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 18384, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 18384, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 18384, "tid": 1560, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 18384, "tid": 1560, "ts": 1749665924356173, "dur": 580, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924360469, "dur": 652, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 18384, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 18384, "tid": 1, "ts": 1749665923062506, "dur": 4241, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749665923066751, "dur": 31929, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 18384, "tid": 1, "ts": 1749665923098690, "dur": 27925, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924361125, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 18384, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923057075, "dur": 51, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923057127, "dur": 1292685, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923057719, "dur": 2134, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923059858, "dur": 943, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923060803, "dur": 644, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923061451, "dur": 11, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923061463, "dur": 488, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923061955, "dur": 5, "ph": "X", "name": "ProcessMessages 8276", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923061960, "dur": 75, "ph": "X", "name": "ReadAsync 8276", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062037, "dur": 4, "ph": "X", "name": "ProcessMessages 7856", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062043, "dur": 19, "ph": "X", "name": "ReadAsync 7856", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062065, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062093, "dur": 21, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062117, "dur": 20, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062139, "dur": 94, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062237, "dur": 56, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062297, "dur": 2, "ph": "X", "name": "ProcessMessages 2278", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062299, "dur": 36, "ph": "X", "name": "ReadAsync 2278", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062338, "dur": 25, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062365, "dur": 21, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062389, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062409, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062429, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062449, "dur": 18, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062469, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062490, "dur": 41, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062535, "dur": 18, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062555, "dur": 29, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062588, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062616, "dur": 25, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062642, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062644, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062668, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062691, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062714, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062735, "dur": 30, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062768, "dur": 22, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062793, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062813, "dur": 20, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062835, "dur": 19, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062856, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062879, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062900, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062923, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062942, "dur": 19, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062964, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923062985, "dur": 36, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063024, "dur": 18, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063045, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063066, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063087, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063117, "dur": 27, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063147, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063169, "dur": 17, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063188, "dur": 88, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063278, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063301, "dur": 21, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063326, "dur": 23, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063351, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063372, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063393, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063412, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063433, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063454, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063478, "dur": 19, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063499, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063519, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063543, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063563, "dur": 21, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063587, "dur": 56, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063646, "dur": 21, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063669, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063688, "dur": 209, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063899, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063921, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063944, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063965, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923063987, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064007, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064027, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064052, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064074, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064095, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064118, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064140, "dur": 38, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064180, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064198, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064220, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064242, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064264, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064285, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064306, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064328, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064351, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064373, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064394, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064396, "dur": 19, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064417, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064419, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064440, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064464, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064487, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064509, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064511, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064539, "dur": 18, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064560, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064586, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064611, "dur": 20, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064634, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064657, "dur": 34, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064695, "dur": 18, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064717, "dur": 23, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064742, "dur": 21, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064766, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064788, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064809, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064831, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923064852, "dur": 441, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065296, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065297, "dur": 70, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065369, "dur": 4, "ph": "X", "name": "ProcessMessages 8100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065373, "dur": 93, "ph": "X", "name": "ReadAsync 8100", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065469, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065471, "dur": 45, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065517, "dur": 2, "ph": "X", "name": "ProcessMessages 3141", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065520, "dur": 27, "ph": "X", "name": "ReadAsync 3141", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065550, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065571, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065597, "dur": 21, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065621, "dur": 20, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065644, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065665, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065687, "dur": 22, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065714, "dur": 36, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065752, "dur": 20, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065775, "dur": 19, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065796, "dur": 21, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065819, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065842, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065843, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065865, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065889, "dur": 24, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065916, "dur": 19, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065937, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065958, "dur": 22, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923065983, "dur": 21, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066006, "dur": 21, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066031, "dur": 19, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066053, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066079, "dur": 20, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066102, "dur": 27, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066132, "dur": 17, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066151, "dur": 20, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066174, "dur": 21, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066198, "dur": 22, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066221, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066224, "dur": 20, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066247, "dur": 20, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066270, "dur": 26, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066297, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066299, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066322, "dur": 41, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066365, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066366, "dur": 21, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066390, "dur": 31, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066443, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066477, "dur": 2, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066479, "dur": 26, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066509, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066531, "dur": 23, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066557, "dur": 21, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066580, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066581, "dur": 20, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066605, "dur": 20, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066627, "dur": 22, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066652, "dur": 99, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066763, "dur": 5, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066770, "dur": 103, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066877, "dur": 2, "ph": "X", "name": "ProcessMessages 2697", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066880, "dur": 50, "ph": "X", "name": "ReadAsync 2697", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066932, "dur": 2, "ph": "X", "name": "ProcessMessages 2185", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066935, "dur": 38, "ph": "X", "name": "ReadAsync 2185", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066975, "dur": 1, "ph": "X", "name": "ProcessMessages 1616", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923066976, "dur": 30, "ph": "X", "name": "ReadAsync 1616", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067009, "dur": 19, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067030, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067051, "dur": 21, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067075, "dur": 18, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067096, "dur": 54, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067152, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067176, "dur": 18, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067196, "dur": 25, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067224, "dur": 21, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067248, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067271, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067293, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067318, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067344, "dur": 102, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067454, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067456, "dur": 36, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067494, "dur": 1, "ph": "X", "name": "ProcessMessages 2051", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067495, "dur": 37, "ph": "X", "name": "ReadAsync 2051", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067535, "dur": 28, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067566, "dur": 19, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067588, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067608, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067610, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067633, "dur": 28, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067664, "dur": 20, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067687, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067708, "dur": 19, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067729, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067752, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067773, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067795, "dur": 19, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067816, "dur": 29, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067847, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067873, "dur": 20, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067896, "dur": 19, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067919, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067940, "dur": 18, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067961, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923067982, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068003, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068022, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068041, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068072, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068098, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068100, "dur": 23, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068125, "dur": 47, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068175, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068197, "dur": 25, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068225, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068245, "dur": 45, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068292, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068313, "dur": 88, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068405, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068407, "dur": 32, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068441, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068443, "dur": 28, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068474, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068499, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068501, "dur": 19, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068522, "dur": 48, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068573, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068624, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068625, "dur": 29, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068657, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068678, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068700, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068702, "dur": 53, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068757, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068780, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068805, "dur": 46, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068853, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068888, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068889, "dur": 16, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068909, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068948, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068983, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923068985, "dur": 16, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069003, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069042, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069063, "dur": 25, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069091, "dur": 42, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069136, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069158, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069179, "dur": 44, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069226, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069267, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069289, "dur": 41, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069332, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069354, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069376, "dur": 17, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069396, "dur": 35, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069433, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069457, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069478, "dur": 45, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069525, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069550, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069572, "dur": 40, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069614, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069637, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069658, "dur": 17, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069679, "dur": 32, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069714, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069811, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069840, "dur": 1, "ph": "X", "name": "ProcessMessages 1315", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069842, "dur": 23, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069867, "dur": 16, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069885, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069939, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069966, "dur": 21, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923069989, "dur": 51, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070043, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070066, "dur": 20, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070089, "dur": 44, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070135, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070163, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070165, "dur": 21, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070188, "dur": 40, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070230, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070256, "dur": 19, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070290, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070311, "dur": 23, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070336, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070360, "dur": 19, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070383, "dur": 45, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070431, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070454, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070478, "dur": 37, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070518, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070542, "dur": 21, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070566, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070588, "dur": 41, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070632, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070654, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070676, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070678, "dur": 22, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070702, "dur": 22, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070727, "dur": 27, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070756, "dur": 42, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070801, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070824, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070846, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070848, "dur": 41, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070892, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070930, "dur": 19, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070951, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070952, "dur": 17, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923070972, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071010, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071041, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071043, "dur": 18, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071063, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071108, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071130, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071132, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071153, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071155, "dur": 39, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071196, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071223, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071245, "dur": 43, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071290, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071312, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071334, "dur": 44, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071382, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071405, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071407, "dur": 20, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071429, "dur": 36, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071468, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071492, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071515, "dur": 39, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071557, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071612, "dur": 30, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071644, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071667, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071688, "dur": 15, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071706, "dur": 34, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071743, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071766, "dur": 18, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071787, "dur": 39, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071829, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071850, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071873, "dur": 41, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071916, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071938, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071961, "dur": 15, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923071979, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072011, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072032, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072054, "dur": 19, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072075, "dur": 18, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072096, "dur": 18, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072116, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072136, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072177, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072198, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072220, "dur": 71, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072294, "dur": 78, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072376, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072378, "dur": 32, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072412, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072414, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072443, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072445, "dur": 38, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072487, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072511, "dur": 19, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072532, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072550, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072584, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072605, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072625, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072644, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072682, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072704, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072706, "dur": 20, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072729, "dur": 37, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072768, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072793, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072814, "dur": 42, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072858, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072880, "dur": 19, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072902, "dur": 40, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072944, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072967, "dur": 28, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923072998, "dur": 43, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073044, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073066, "dur": 18, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073087, "dur": 49, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073138, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073166, "dur": 18, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073187, "dur": 41, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073231, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073252, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073273, "dur": 15, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073291, "dur": 36, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073329, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073350, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073369, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073393, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073425, "dur": 18, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073445, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073464, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073504, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073535, "dur": 17, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073554, "dur": 103, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073661, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073699, "dur": 1, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073701, "dur": 38, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073740, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073742, "dur": 24, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073769, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073830, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073864, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073867, "dur": 89, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923073960, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074027, "dur": 2, "ph": "X", "name": "ProcessMessages 1905", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074030, "dur": 35, "ph": "X", "name": "ReadAsync 1905", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074067, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074068, "dur": 17, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074088, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074107, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074169, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074191, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074213, "dur": 19, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074234, "dur": 18, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074255, "dur": 17, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074274, "dur": 129, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074406, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074433, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074435, "dur": 121, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074562, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074599, "dur": 256, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074857, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074944, "dur": 5, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074950, "dur": 44, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923074997, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075000, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075037, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075039, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075073, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075075, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075119, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075121, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075154, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075156, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075186, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075212, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075241, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075243, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075282, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075284, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075317, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075319, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075352, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075430, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075461, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075463, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075492, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075494, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075527, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075529, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075556, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075559, "dur": 13, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075574, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075600, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075631, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075634, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075660, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075661, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075691, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075693, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075717, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075720, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075748, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075750, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075787, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075789, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075834, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075836, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075864, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075867, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075894, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075896, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075929, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075932, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075969, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923075971, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076004, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076006, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076043, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076044, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076089, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076124, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076127, "dur": 46, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076177, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076180, "dur": 39, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076221, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076224, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076261, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076263, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076296, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076298, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076329, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076331, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076362, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076364, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076398, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076400, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076433, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076436, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076469, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076472, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076511, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076514, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076541, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076543, "dur": 27, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076573, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076574, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076611, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076614, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076642, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076643, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076670, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076672, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076704, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076706, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076734, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076736, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076766, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076768, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076802, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076804, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076833, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076835, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076861, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076864, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076891, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076892, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076922, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076924, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076948, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076950, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076975, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923076976, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077001, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077002, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077030, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077033, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077053, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077054, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077084, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077119, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077121, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077160, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077227, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077229, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923077260, "dur": 14638, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923091905, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923091909, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923091960, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923091963, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923092009, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923092011, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923092101, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923092142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923092144, "dur": 6972, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099123, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099130, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099184, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099186, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099218, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099220, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099253, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099273, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099554, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099577, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099655, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099657, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099817, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099857, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099859, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099885, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099905, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923099930, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100004, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100029, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100052, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100217, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100239, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100358, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100383, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100434, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100463, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100497, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100525, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100527, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100585, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100610, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100696, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100725, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100727, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100891, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100919, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100920, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100945, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923100965, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101016, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101038, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101083, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101108, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101153, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101180, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101224, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101247, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101276, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101326, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101328, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101388, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101412, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101414, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101436, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101438, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101471, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101473, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101500, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101502, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101566, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101595, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101598, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101623, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101624, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101651, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101676, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101829, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101855, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101856, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101878, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101903, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101936, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101960, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101961, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923101982, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102022, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102046, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102077, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102079, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102114, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102150, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102152, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102176, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102177, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102230, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102255, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102289, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102308, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102383, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102435, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102437, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102468, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102469, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102526, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102553, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102702, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102726, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102747, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102777, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102811, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102815, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102875, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102921, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102923, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102963, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102998, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923102999, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103028, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103030, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103125, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103154, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103185, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103187, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103246, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103284, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103286, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103318, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103349, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103351, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103371, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103427, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103464, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103467, "dur": 90, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103560, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103589, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103661, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103687, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103689, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103740, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103761, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103783, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103827, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103872, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103874, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103907, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103937, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923103974, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104006, "dur": 602, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104612, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104670, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104672, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104720, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104722, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104758, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104760, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104792, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104831, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104874, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104915, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104917, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104950, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923104986, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105082, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105117, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105153, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105184, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105251, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105280, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105281, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105357, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105377, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105402, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105403, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105432, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105455, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105497, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105518, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105580, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105604, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105606, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105784, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105815, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105817, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105841, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105921, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105939, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923105943, "dur": 426, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923106372, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923106390, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923106391, "dur": 49488, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923155888, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923155891, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923155944, "dur": 2002, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923157948, "dur": 5942, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163900, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163903, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163933, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923163984, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164021, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164023, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164047, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164049, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164209, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164251, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164253, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164302, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164488, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164507, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164897, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164926, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164928, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164961, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164963, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923164991, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165336, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165363, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165413, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165448, "dur": 439, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165892, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923165911, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166176, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166209, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166552, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166568, "dur": 352, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166925, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923166951, "dur": 481, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167438, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167462, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167464, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167513, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167547, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167579, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167608, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167610, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167725, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923167764, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168310, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168326, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168362, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168383, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168629, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168660, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168738, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168774, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168868, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923168897, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923169145, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923169175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923169177, "dur": 1091, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170271, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170314, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170339, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170359, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170572, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170596, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170833, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170880, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923170882, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171085, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171127, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171178, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171222, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171264, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171296, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171303, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171379, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171412, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171414, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171679, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171695, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171744, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171769, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171791, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171824, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171826, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171851, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171876, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171877, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171922, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171950, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171952, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171973, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923171997, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172036, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172038, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172070, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172089, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172118, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172146, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172171, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172200, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172225, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172243, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172261, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172294, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172296, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172328, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172329, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172349, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172368, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172412, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172413, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172440, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172463, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172470, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172502, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172504, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172530, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172552, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172579, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172600, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172632, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172634, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172658, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172688, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172689, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172723, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172725, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172747, "dur": 66, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172816, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172818, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172879, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172881, "dur": 53, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172938, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172940, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172980, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923172982, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923173089, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923173090, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923173133, "dur": 177865, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923351008, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923351012, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923351056, "dur": 3175, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923354235, "dur": 59388, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923413630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923413633, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923413655, "dur": 448944, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862606, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862609, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862628, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862631, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862655, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923862658, "dur": 81663, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923944330, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923944333, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923944354, "dur": 34, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923944389, "dur": 7068, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923951465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923951467, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923951505, "dur": 20, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923951526, "dur": 9936, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923961469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923961471, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923961490, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923961493, "dur": 2124, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923963624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923963629, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923963659, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923963689, "dur": 5069, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923968763, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923968794, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665923968795, "dur": 73539, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924042348, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924042354, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924042417, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924042421, "dur": 295552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924337990, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924337993, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924338016, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924338038, "dur": 1235, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924339280, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924339283, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924339350, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924339380, "dur": 744, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924340145, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924340148, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924340177, "dur": 446, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 18384, "tid": 12884901888, "ts": 1749665924340626, "dur": 9006, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924361143, "dur": 1166, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 18384, "tid": 8589934592, "ts": 1749665923055104, "dur": 71560, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749665923126667, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 18384, "tid": 8589934592, "ts": 1749665923126671, "dur": 3947, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924362312, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 18384, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 18384, "tid": 4294967296, "ts": 1749665923020884, "dur": 1329586, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749665923023830, "dur": 6369, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749665924350482, "dur": 3465, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749665924352288, "dur": 74, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 18384, "tid": 4294967296, "ts": 1749665924354000, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924362317, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749665923045902, "dur": 1909, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665923047820, "dur": 653, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665923048594, "dur": 67, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749665923048661, "dur": 521, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665923049883, "dur": 11210, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665923061480, "dur": 444, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749665923062388, "dur": 894, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665923063694, "dur": 155, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749665923063867, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_436C0E2610862891.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749665923064205, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749665923064483, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749665923067545, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749665923049204, "dur": 27376, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665923076592, "dur": 1264927, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665924341522, "dur": 242, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665924341765, "dur": 260, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665924342203, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749665924342283, "dur": 1628, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749665923049142, "dur": 27461, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923076642, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923076757, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923076753, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923076924, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923076974, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923077348, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923077347, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923077451, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923077450, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_ADDD82C2A90EA988.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923077631, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923078014, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665923078302, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923078552, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923078743, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923078925, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749665923079125, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923079325, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923080746, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923082894, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923084412, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923086954, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Connections\\ValueConnectionWidget.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749665923085919, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923088127, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923089671, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923091261, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923092450, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923094358, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923095471, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923096937, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923098383, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923099785, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923101026, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923101377, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923101899, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923102601, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923102700, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923103423, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923102665, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923103540, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923103688, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923103856, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923104251, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923104818, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923105186, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923105253, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749665923105394, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923105784, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923106022, "dur": 59290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923165843, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923166120, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923167667, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923165313, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923168067, "dur": 1062, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923169624, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923169812, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923169140, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923171677, "dur": 1371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749665923173988, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll"}}, {"pid": 12345, "tid": 1, "ts": 1749665923173056, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749665923175213, "dur": 1166298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923049172, "dur": 27489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923076695, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923076788, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749665923076672, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923076849, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923076911, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923076909, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077018, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077017, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0DA2B5FA22101DBE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077160, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923077238, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077236, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077423, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923077532, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077531, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077783, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923077848, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923077915, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078001, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923078190, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078253, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078360, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078414, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078473, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078640, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078692, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078746, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078835, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078893, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078947, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079056, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079277, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079452, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079516, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079643, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079805, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079943, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923079998, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080067, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080167, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080319, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080594, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080647, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923080931, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923081044, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923081249, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923081480, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923081722, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923081981, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923082180, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923082426, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923082704, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923082932, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083025, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083099, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083287, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083362, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083422, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083529, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083723, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083839, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083897, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923083952, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084058, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084198, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084354, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084605, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084830, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923084960, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085035, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085166, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085309, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085360, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085616, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923085906, "dur": 429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923086457, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923086744, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923086862, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923087000, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923087655, "dur": 217, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923087873, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923088071, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923088182, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923088312, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923088757, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923088858, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089052, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089106, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089298, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089392, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089449, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089552, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089744, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923089866, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090072, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090130, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090182, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090328, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090555, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090790, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923090900, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923091139, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923091242, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923091342, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923091615, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923091732, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923092134, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ScrollRect.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923092260, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923092506, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923092559, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923092759, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093008, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093231, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093500, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093620, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093778, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923078077, "dur": 15847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923093925, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923094155, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923095497, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923097010, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923098693, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923100165, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923101013, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923101374, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923101871, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923102551, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.searcher@4.9.2\\Editor\\Searcher\\SearcherControl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1749665923102058, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923102643, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923103482, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923103543, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1749665923103540, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_1A2083B43FB0BF18.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923103602, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923104242, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923104354, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923104414, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923105265, "dur": 2372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923107637, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749665923107729, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923107954, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923108027, "dur": 55571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923164177, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923165415, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923166080, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.AccessControl.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923166436, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923163600, "dur": 3504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923167105, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923168168, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923168976, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Results.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923169703, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923167551, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923171013, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923171734, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923171099, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749665923173624, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174001, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174101, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174204, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174268, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174340, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 2, "ts": 1749665923174405, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174460, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174561, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174653, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174721, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174779, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923174898, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749665923175144, "dur": 1166400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923049204, "dur": 27471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923076709, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923076688, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923076804, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923076871, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923076870, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923076928, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923076985, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923076983, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077156, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923077219, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077218, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077504, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077503, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077678, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077677, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077840, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077839, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923077943, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923078357, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923078585, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923078757, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923078848, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923078912, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923079009, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923079195, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923079280, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923079396, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923081636, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923082530, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923083884, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923085139, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923087044, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\CustomRenderTexture\\CustomTextureSlice.cs"}}, {"pid": 12345, "tid": 3, "ts": 1749665923086208, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923088137, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923089347, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923090687, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923091907, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923093502, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923094631, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923095510, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923097375, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923098763, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923100018, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923101067, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923101367, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923101872, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923102022, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923102312, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923102540, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923102091, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923103227, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923103612, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923103956, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923104220, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923103886, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923104824, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923105236, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749665923105409, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923105472, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923105913, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923106066, "dur": 57485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923163553, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923165810, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923166428, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923167595, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923167670, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Results.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923168106, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923166427, "dur": 3057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923169485, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923171734, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923172329, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923169664, "dur": 2731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923172395, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749665923172749, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923173337, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749665923172524, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749665923175308, "dur": 1166308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923049158, "dur": 27461, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923076642, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923076762, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923076756, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923076851, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923076919, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923076918, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077099, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923077193, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077191, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077268, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923077324, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077323, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0F9F7C5D1B98DB2C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077377, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923077508, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923077708, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077773, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923077847, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665923077965, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078027, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749665923078141, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078316, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749665923078372, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078466, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078572, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078635, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078743, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923078852, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923079120, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923079305, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923080903, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923081448, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923083444, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923084684, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923086034, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923087036, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Manipulators\\Scrollable.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749665923086923, "dur": 1798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923088722, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923089759, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923090956, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923091924, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923093561, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923094924, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923096280, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923097818, "dur": 2019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923099880, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923100790, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923101378, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923101894, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923102749, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923102824, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923103500, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923104207, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923104334, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923104483, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923104631, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923105345, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923105670, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923105776, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923106866, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923107008, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923107101, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923107548, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923107633, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923107718, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923107947, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923108057, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749665923108150, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923108616, "dur": 244436, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923357491, "dur": 58131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923357259, "dur": 58425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923415686, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923415821, "dur": 446739, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1749665923415819, "dur": 447794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923864474, "dur": 123, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749665923864800, "dur": 88835, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749665923970862, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749665923970862, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1749665923970969, "dur": 370597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923049239, "dur": 27467, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923076730, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923076717, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923076942, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923077028, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077026, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077092, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923077165, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077163, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077290, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077343, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923077411, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077521, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077410, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923077860, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923078282, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923078543, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923078647, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923078703, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923078869, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923079220, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749665923079330, "dur": 2190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923081520, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923082767, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923084651, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923086945, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\KeywordDefinition.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749665923086522, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923088458, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923089654, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923091305, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923093240, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923094755, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923096376, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923097731, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923098375, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923099445, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923100716, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923101358, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923101423, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923101870, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923102002, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923102570, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665923103087, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923103724, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923103792, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923104053, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923104284, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923104419, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923105260, "dur": 1768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923107029, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749665923107159, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749665923107409, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923107496, "dur": 56101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923163652, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923163894, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923164027, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923166397, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923163598, "dur": 3497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665923167096, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923167214, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665923169598, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923171279, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Registry.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923172210, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923172789, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923169688, "dur": 3516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749665923173205, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923173414, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923173614, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174066, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174155, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174428, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174537, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923174537, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1749665923174634, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174727, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174797, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174872, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174939, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923174996, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923175065, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749665923175263, "dur": 1166345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923049231, "dur": 27458, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923076705, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923076696, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077087, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077086, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D0034867E905C483.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077267, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077265, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1CE9B2E479BEC5CA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077400, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923077466, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077465, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077678, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923077745, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077854, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923077939, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923078126, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078401, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078561, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078624, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078687, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078745, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078834, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078940, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079008, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079254, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079344, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079396, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079452, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079516, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079804, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923079943, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080008, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080113, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080319, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080606, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080765, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923080968, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923081069, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923081127, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923081414, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923081910, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923082078, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923082137, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923082428, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923082876, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083008, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083104, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083177, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083232, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083287, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083366, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083529, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083804, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083925, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923083976, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084076, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084265, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084393, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084576, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084636, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084689, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923084960, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085022, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085141, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085196, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085461, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085587, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923085776, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923086408, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923086556, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923086666, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923086809, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923086861, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923087030, "dur": 625, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923087870, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923088133, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923088246, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923088372, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923088469, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923088874, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923089012, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923089224, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923089375, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923089473, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923089627, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923090177, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\ITestRunnerListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923090408, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923090647, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923090764, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923090863, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091118, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091175, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091288, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091413, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091555, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091681, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091826, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091888, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091941, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923091996, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092095, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092234, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092405, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\FloatEqualityComparer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092536, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092633, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092761, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092844, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092896, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\MonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923092947, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PostBuildCleanupAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923093219, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923093329, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923093430, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3EqualityComparer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749665923093575, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923078008, "dur": 15695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923093704, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923094163, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923094507, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923094576, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923094675, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923094953, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095067, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095211, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095360, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095489, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095642, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095913, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923095967, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923096018, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923096155, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923096355, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923096683, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923096897, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097065, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097209, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097345, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097482, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097850, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923097955, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098007, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098074, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098202, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098445, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098500, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098743, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098842, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923098973, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099137, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099277, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099464, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099637, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099757, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099851, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923099955, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100061, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100304, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100637, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100701, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100753, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923100807, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923094320, "dur": 6933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923101406, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923101481, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923101713, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923101858, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923102040, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923102830, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923103155, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749665923103269, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923103590, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923103524, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923104101, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923104386, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1749665923105094, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923105276, "dur": 291, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923106233, "dur": 51828, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1749665923163548, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923166015, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923166237, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923169081, "dur": 722, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923170898, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923169812, "dur": 2620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923172432, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923174314, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923174502, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1749665923172496, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749665923175038, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749665923175195, "dur": 1166351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923049267, "dur": 27466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923076772, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923076746, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665923076960, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923077074, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923077073, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_7591B359D09ADD67.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665923077436, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923077734, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665923077899, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923077983, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923078039, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665923078129, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923078219, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923078507, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665923078699, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923078781, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923078948, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923079003, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749665923079059, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923079296, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923079371, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923081199, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923082636, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923084183, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923086879, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\LiteralOption.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749665923085684, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923087652, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923088860, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923089790, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923090982, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923092437, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923093736, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923094992, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923096235, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923098143, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923099805, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923100015, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923101168, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923101379, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923101898, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665923102166, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923103671, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\EditorProgressBar.cs"}}, {"pid": 12345, "tid": 7, "ts": 1749665923102582, "dur": 1706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665923104288, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923104670, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749665923104839, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923104923, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749665923105413, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923105586, "dur": 31864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923137454, "dur": 26101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923163556, "dur": 2414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665923165971, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923166425, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923166603, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923167039, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923167557, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cors.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923168314, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923168443, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923169002, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923169122, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923166207, "dur": 3940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665923170148, "dur": 2385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923174504, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 7, "ts": 1749665923172538, "dur": 2178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749665923174721, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923174923, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923174991, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1749665923175057, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749665923175206, "dur": 1166350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923049297, "dur": 27488, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923076817, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923076808, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923076899, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923076972, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923076970, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923077057, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923077128, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923077126, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923077287, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923077443, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923077442, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923077680, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923077789, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923077874, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923077937, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078032, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749665923078089, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078157, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078358, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078572, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078689, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078755, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078863, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923078983, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749665923079257, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923079317, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923080807, "dur": 2031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923082839, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923084427, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923086933, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Util\\Documentation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749665923085988, "dur": 2466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923088454, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923090017, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923091846, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923093615, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923094960, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923096060, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923097180, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923098978, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923099793, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923100790, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923101358, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923101863, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923102073, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923103274, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923102732, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923103470, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923103683, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923104229, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923104453, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923105259, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923106329, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923106458, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923107029, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923107122, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923107190, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923107293, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923107578, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923107657, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749665923107730, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923107941, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923108032, "dur": 55546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923163714, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923166080, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Cng.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923166427, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923163590, "dur": 3871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923167462, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923168320, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Web.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923167645, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923170012, "dur": 835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923173132, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923170858, "dur": 2917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749665923173775, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923174078, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923174200, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923174382, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923174450, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923174565, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923175003, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749665923175086, "dur": 795823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665923970911, "dur": 73430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665923970910, "dur": 73434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749665924044346, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749665924044493, "dur": 297055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923049342, "dur": 27515, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923076878, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923076869, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923076962, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923077062, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077060, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077139, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923077224, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077222, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2FC980318CB13784.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077278, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923077343, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077342, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4EAE93D6B17EFAD3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077398, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923077526, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077524, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077727, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.Unsafe.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077726, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923077994, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749665923078091, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923078695, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923078890, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923079083, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923079186, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923079269, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923079430, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923080713, "dur": 2115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923082828, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923084797, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923087024, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Events\\GlobalMessageListenerEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749665923085840, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923087753, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923088814, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923089827, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923091694, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923093283, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923094741, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923096124, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923097628, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923098974, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923100017, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923100889, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923101379, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923101868, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923102024, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923102109, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923102957, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923103243, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923103590, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923103441, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923104141, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923104350, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923104823, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923105188, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923105274, "dur": 2786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923108061, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749665923108165, "dur": 55428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923163595, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923166015, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923167596, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordbi.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923167936, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923168119, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923166244, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923168992, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923170076, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923170347, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923170905, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-conio-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923171156, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923171705, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749665923169736, "dur": 3009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923172745, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923172821, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749665923175111, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749665923175262, "dur": 1166344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923049372, "dur": 27569, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923077009, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077007, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_85F6C15D73A578BD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077084, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923077186, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077184, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5A3FD65734E3273.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077305, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077304, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077388, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923077449, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077448, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077708, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923077801, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749665923077883, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923077983, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923078039, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923078127, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923078372, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923078679, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923078794, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749665923079319, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923080712, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923082381, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923083659, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923085744, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Variables\\VariableKindOption.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749665923086970, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IsVariableDefinedUnitOption.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749665923085379, "dur": 2638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923088018, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923089469, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923090722, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923092220, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923093555, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923095462, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923097132, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923098023, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923099140, "dur": 1812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923100952, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923101378, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923102019, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923102254, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923102440, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665923103003, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923103161, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923103383, "dur": 893, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923104360, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923104279, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665923104942, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923105132, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923105254, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923105433, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923105552, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665923105958, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923106147, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749665923106317, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749665923106733, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923106975, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923107057, "dur": 56512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923163890, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923164411, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923164732, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923165240, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.dll"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 947, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 4697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": ****************, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923171536, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923173393, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923173841, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749665923170983, "dur": 2972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749665923173956, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923174093, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923174195, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923174483, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923174727, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923174988, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923175076, "dur": 788405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749665923963488, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749665923963487, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749665923963689, "dur": 2134, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1749665923965829, "dur": 375802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923049385, "dur": 27595, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923076998, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923076993, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_F782C228CEDFF0CD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665923077355, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923077354, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_51DC255B330D5605.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665923077515, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923077699, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923077817, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749665923078095, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749665923078216, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078317, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078384, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078579, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078684, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078759, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923078925, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923079067, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749665923079118, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923079292, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923079456, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923080506, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923082091, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923083650, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923084699, "dur": 1961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923087025, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Collections\\DefineCollection.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749665923086661, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923088493, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923089252, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923090678, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923092538, "dur": 1948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923094487, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923096177, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923097695, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923098988, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923100618, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923101376, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923102054, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665923102217, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923102550, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923102274, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923103020, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923103590, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923103687, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923104227, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor@2c0153a9ba\\Editor\\UsageUtility.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749665923103401, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923104374, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923105007, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923105261, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749665923105578, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923105636, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923105936, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923106029, "dur": 57566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923166120, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923166397, "dur": 810, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923163596, "dur": 3697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923167294, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923167597, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923168182, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923168531, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923168834, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923169702, "dur": 385, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Debug.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923170899, "dur": 817, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923172514, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923167596, "dur": 5071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923172668, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923174504, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923174610, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749665923172789, "dur": 2139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749665923174929, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749665923175115, "dur": 1166399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923049409, "dur": 27594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923077029, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077013, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077172, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923077412, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077411, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077529, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077528, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1B6116B393E72286.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923077922, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923078321, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749665923078474, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923078811, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749665923079278, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923081350, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923083057, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923084196, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923085239, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923086998, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\EnumPropertyDrawer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749665923086998, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923088668, "dur": 1499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923090167, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923091458, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923092503, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923094175, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923096104, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923097323, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923098903, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923099915, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923100816, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923101399, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923101855, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923101992, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923102551, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923102086, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923103001, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923103476, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923103611, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923103814, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923104266, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923104369, "dur": 864, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923105252, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923105420, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923105522, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923105995, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923106111, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923106288, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923106899, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923107024, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749665923107131, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923107198, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923107525, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923107608, "dur": 55950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923165025, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923165166, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923165793, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923165933, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923163561, "dur": 3447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923167009, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923167192, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923167110, "dur": 2627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923169740, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923169808, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923172177, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923174110, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1749665923172579, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749665923174913, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923175036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749665923175092, "dur": 1166430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923049428, "dur": 27604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923077041, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749665923077036, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665923077402, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923077537, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923077716, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923078267, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749665923078454, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923078707, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923079288, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923080414, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923081807, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923083719, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923085013, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923086940, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\AssetCallbacks\\CreateLitShaderGraph.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749665923086380, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923088201, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923089057, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923090166, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923091229, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923092818, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923094455, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923095942, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923097445, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923098992, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923100627, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923101360, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923101882, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665923102699, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SubMeshUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749665923102097, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923102877, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923103153, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923103398, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665923103614, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665923103792, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923104143, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923104531, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923104647, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923105264, "dur": 1932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923107197, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749665923107308, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923107376, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923107724, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923107824, "dur": 55740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923164707, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749665923164989, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749665923165534, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749665923163567, "dur": 3299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923166866, "dur": 1905, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923169625, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1749665923168781, "dur": 2515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923171296, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923171374, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749665923173637, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923174263, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923174349, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923174438, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923174527, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923174765, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923175062, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749665923175220, "dur": 1166359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923049447, "dur": 27592, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923077048, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923077044, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665923077171, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923077287, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923077286, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A857FD1C4743904D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665923077371, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923077502, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923077709, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923077895, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923078052, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923078457, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923078558, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923078924, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923078994, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654656162358534522.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665923079050, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923079130, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8336928665352126947.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749665923079182, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923079338, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923080487, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923082007, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923083145, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923084341, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923085812, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_2_3.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749665923085213, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923086890, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Descriptors\\FieldDescriptor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749665923086634, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923088988, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923090468, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923091874, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923093424, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923094683, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923095705, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923097139, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923098698, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923100081, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923101121, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923101373, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923101871, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665923102698, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923102842, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.2\\Runtime\\Tiles\\AnimatedTile\\AnimatedTile.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749665923102242, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665923103066, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923103299, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923103444, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923103616, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665923104189, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923104347, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749665923104754, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923104535, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749665923105955, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923106075, "dur": 57486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923165412, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923163562, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665923166159, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923167234, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923166706, "dur": 3163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665923169869, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923170083, "dur": 862, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923172611, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923173029, "dur": 688, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1749665923169953, "dur": 3915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749665923173869, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923174043, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923174195, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923174407, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923174576, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923174696, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749665923175244, "dur": 1166356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923049466, "dur": 27588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923077084, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077067, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_116A6FADE34C1D2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077184, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923077261, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077260, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_C7611EE8A5C55055.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077316, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923077381, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077379, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_7406CA3F32B5E22E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923077472, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923077765, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923078035, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749665923078267, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749665923078346, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923078538, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749665923078595, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923078724, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923078866, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923079211, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749665923079297, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923081244, "dur": 2276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923083520, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923086932, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditorTool\\PathEditorTool.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749665923084809, "dur": 2825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923087638, "dur": 1156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923088794, "dur": 2105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923090899, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923092282, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923094163, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923096023, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923097096, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923098178, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923099362, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923100634, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923101357, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923101858, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923102250, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923102124, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923102779, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923103175, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923103423, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923103548, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923103376, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923104001, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923104192, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749665923104366, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923104797, "dur": 1208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923106050, "dur": 57550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923164637, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923166080, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923166202, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923163601, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923166257, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923167594, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923166470, "dur": 2570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923169041, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923169702, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1749665923169686, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749665923172185, "dur": 1116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923173311, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923174089, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923174700, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923174761, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923174824, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923174951, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749665923175086, "dur": 1166448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923049486, "dur": 27582, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923077084, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077077, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F7F95DB68C78C481.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077360, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077359, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E37D1A982AA61309.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077528, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077527, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077747, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923077806, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923077885, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923077950, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749665923078321, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749665923078398, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923078476, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923078593, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923078683, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923078908, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923079217, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749665923079314, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923080332, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923081556, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923083048, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923084090, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923085882, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\NesterUnitAnalyser.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749665923087033, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\MultiInputUnitNumericDescriptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749665923085617, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923087973, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923089669, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923090839, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923091867, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923093444, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923095262, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923096997, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923097982, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923099561, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923100787, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923101361, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923101857, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923101998, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923102843, "dur": 385, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Deprecated.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749665923102094, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923103522, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923104168, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923104496, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923104880, "dur": 1188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923106114, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923106247, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923106674, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923106743, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923106811, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749665923106938, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923107211, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923107327, "dur": 56265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923164364, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923165544, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Abstractions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923165773, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Net.Http.Headers.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923166081, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923163593, "dur": 3627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923167241, "dur": 3286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923171418, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923171737, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-timezone-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923172957, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923173394, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 16, "ts": 1749665923170535, "dur": 3506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749665923174042, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923174169, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923174231, "dur": 960, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749665923175194, "dur": 1166392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923049512, "dur": 27570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923077100, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923077091, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_523CC7A31BEDDB75.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749665923077348, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923077538, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923077537, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749665923077794, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923077866, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749665923077938, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923078142, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923078344, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923078809, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749665923078921, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749665923079026, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1749665923079300, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923081250, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923082125, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923083630, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923085010, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923086992, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Processors\\Generator.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749665923086470, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923088345, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923089883, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923091478, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923093378, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923094670, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923096777, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923098293, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923099205, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923100247, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923100977, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923101373, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923101856, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749665923103134, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MessageListener.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749665923103274, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnButtonClickMessageListener.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749665923103514, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Profiling\\ProfiledSegment.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749665923102057, "dur": 1911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749665923103968, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923104061, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923104347, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749665923104571, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923105394, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\CustomEvent.cs"}}, {"pid": 12345, "tid": 17, "ts": 1749665923104651, "dur": 1406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749665923106057, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923106324, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1749665923106465, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1749665923106786, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923106892, "dur": 56661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923165935, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923166119, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923163554, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749665923166335, "dur": 1769, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923168119, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923168639, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923169046, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 17, "ts": 1749665923168112, "dur": 3271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1749665923171383, "dur": 2104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923173497, "dur": 993, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923174740, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923174863, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923174928, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923174986, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1749665923175093, "dur": 1166423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923049537, "dur": 27619, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923077181, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923077170, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749665923077345, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923077344, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6555E2061ABDDC70.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749665923077506, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923077774, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923077837, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749665923077917, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078009, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078079, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078145, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078227, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078641, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078782, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923078924, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1749665923079148, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923079332, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923080549, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923081337, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923082671, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923083763, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923085868, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_6_to_1_7.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749665923085147, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923087025, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\IdentifierField.cs"}}, {"pid": 12345, "tid": 18, "ts": 1749665923086905, "dur": 2473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923089379, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923090741, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923092307, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923093789, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923094978, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923096298, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923098212, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923099604, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923100300, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923100688, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923101359, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923101860, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749665923102039, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923102149, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749665923102523, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923102819, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923103306, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1749665923103629, "dur": 1105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923104903, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923104740, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1749665923105240, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923105344, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923105888, "dur": 60064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923166120, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923166428, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923166524, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923166919, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923167166, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923168320, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923165952, "dur": 3655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749665923169608, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923169857, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923171095, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.Configuration.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923171617, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923171838, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923172309, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923172531, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1749665923169735, "dur": 3501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1749665923173237, "dur": 1659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1749665923175105, "dur": 1166419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923049556, "dur": 27623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923077201, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923077190, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D441A9CFEC32007A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749665923077308, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923077307, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749665923077389, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923077509, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923077650, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923077880, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923077978, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923078156, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923078552, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923078652, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1749665923078705, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923079096, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923080352, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Http.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923079342, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923080969, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923082279, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923083517, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923084678, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923086947, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\LabelSlotControlView.cs"}}, {"pid": 12345, "tid": 19, "ts": 1749665923086706, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923088649, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923089583, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923090809, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923092184, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923093456, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923094433, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923095776, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923097099, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923098020, "dur": 1924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923099944, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923100949, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923101377, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923102060, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749665923102925, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749665923103398, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923103543, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923103721, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923104273, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1749665923104501, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1749665923105437, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923105552, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923106157, "dur": 57392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923163550, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749665923165979, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923166852, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923167193, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923166081, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749665923168216, "dur": 1425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923169693, "dur": 394, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923170525, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923170713, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923170900, "dur": 814, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923173029, "dur": 1097, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 19, "ts": 1749665923169662, "dur": 5036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1749665923174699, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923175067, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1749665923175383, "dur": 1166278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923049574, "dur": 27620, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923077207, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077196, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077316, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077315, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_065B772FA1729652.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077382, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923077441, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077440, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077800, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1749665923077970, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749665923078268, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1749665923078350, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923078596, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923079307, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923081682, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\BindingUtility.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749665923081076, "dur": 2520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923083597, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923084899, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923087022, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\Texture2DArrayPropertyDrawer.cs"}}, {"pid": 12345, "tid": 20, "ts": 1749665923086957, "dur": 1994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923088951, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923090483, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923092211, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923094234, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923095472, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923096911, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923098333, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923099627, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923100763, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923101375, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923101901, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1749665923102340, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923102722, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923103354, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923103878, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923104084, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923104267, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923104397, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923104509, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923105282, "dur": 27543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923132826, "dur": 4581, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923137408, "dur": 26182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923163637, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923166067, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923163591, "dur": 2715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923166307, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923168097, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-1.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923168365, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Abstractions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923169697, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923167158, "dur": 2956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923170115, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923171733, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923173030, "dur": 1045, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923170582, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923174131, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923174385, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923174462, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923174906, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923174969, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923175074, "dur": 182208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923357299, "dur": 505282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923862958, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923357298, "dur": 506550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923864739, "dur": 131, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1749665923864880, "dur": 81619, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1749665923963444, "dur": 376700, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665923963443, "dur": 376704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1749665924340166, "dur": 1254, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1749665924348045, "dur": 3438, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 18384, "tid": 1560, "ts": 1749665924363145, "dur": 20105, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 18384, "tid": 1560, "ts": 1749665924383326, "dur": 2273, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 18384, "tid": 1560, "ts": 1749665924358854, "dur": 27518, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}