using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using System.Text;

/// <summary>
/// Debug tool to help identify UI input issues by providing real-time information about pointer events.
/// Attach to a Canvas GameObject containing UI elements that you want to debug.
/// </summary>
public class UIInputDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    [Tooltip("Whether this component is active")]
    public bool isActive = true;
    
    [Tooltip("Whether to log interactions to the console")]
    public bool logToConsole = false;
    
    [Tooltip("Whether to show visual debug information on screen")]
    public bool showOnScreen = true;
    
    [Tooltip("Key to toggle the on-screen display")]
    public KeyCode toggleKey = KeyCode.F2;
    
    [Header("Visual Settings")]
    [Tooltip("Position of the debug display")]
    public Vector2 displayPosition = new Vector2(10, 10);
    
    [Tooltip("Size of the debug display")]
    public Vector2 displaySize = new Vector2(300, 200);
    
    [Tooltip("Background color of the debug display")]
    public Color backgroundColor = new Color(0, 0, 0, 0.7f);
    
    [Tooltip("Text color for the debug display")]
    public Color textColor = Color.white;
    
    [Tooltip("Whether to draw ray from cursor to hit objects")]
    public bool drawRays = true;
    
    // Internal state
    private StringBuilder debugInfo = new StringBuilder();
    private List<PointerEventData.InputButton> trackedButtons = new List<PointerEventData.InputButton>
    {
        PointerEventData.InputButton.Left,
        PointerEventData.InputButton.Right,
        PointerEventData.InputButton.Middle
    };
    
    private bool isDisplayVisible = true;
    private GUIStyle debugStyle;
    private Rect debugRect;
    private Vector2 lastMousePosition;
    private GameObject lastHitObject;
    
    private void Start()
    {
        // Check if an EventSystem exists
        if (EventSystem.current == null)
        {
            Debug.LogWarning("No EventSystem found in scene. UI interactions won't work.");
            
            // Try to find a UIEventSystemManager to create one
            UIEventSystemManager manager = FindObjectOfType<UIEventSystemManager>();
            if (manager != null)
            {
                manager.EnsureEventSystem();
            }
            else
            {
                Debug.LogError("No UIEventSystemManager found. Add one to a GameObject in your scene.");
            }
        }
    }
    
    private void Update()
    {
        if (!isActive) return;
        
        // Toggle display with the specified key
        if (Input.GetKeyDown(toggleKey))
        {
            isDisplayVisible = !isDisplayVisible;
        }
        
        if (showOnScreen && isDisplayVisible)
        {
            // Update debug info
            UpdateDebugInfo();
        }
        
        // Track mouse position
        lastMousePosition = Input.mousePosition;
        
        // Check for pointer over UI
        CheckPointerOverUI();
    }
    
    private void CheckPointerOverUI()
    {
        if (EventSystem.current == null) return;
        
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = Input.mousePosition
        };
        
        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);
        
        // Process raycast results
        if (results.Count > 0)
        {
            GameObject hitObject = results[0].gameObject;
            
            if (hitObject != lastHitObject)
            {
                lastHitObject = hitObject;
                LogMessage($"Pointer over: {GetObjectPath(hitObject)}");
            }
        }
        else if (lastHitObject != null)
        {
            LogMessage($"Pointer exit: {GetObjectPath(lastHitObject)}");
            lastHitObject = null;
        }
    }
    
    private void UpdateDebugInfo()
    {
        debugInfo.Clear();
        debugInfo.AppendLine("=== UI Input Debugger ===");
        
        // Event System Info
        debugInfo.AppendLine($"EventSystem: {(EventSystem.current != null ? "Active" : "MISSING")}");
        
        if (EventSystem.current != null)
        {
            GameObject currentSelected = EventSystem.current.currentSelectedGameObject;
            debugInfo.AppendLine($"Selected: {(currentSelected != null ? currentSelected.name : "None")}");
        }
        
        // Mouse position
        debugInfo.AppendLine($"Mouse: ({Input.mousePosition.x:F0}, {Input.mousePosition.y:F0})");
        
        // Button states
        foreach (var button in trackedButtons)
        {
            bool isDown = Input.GetMouseButton((int)button);
            debugInfo.AppendLine($"Mouse {button}: {(isDown ? "DOWN" : "up")}");
        }
        
        // Pointer over object
        debugInfo.AppendLine($"Hovering: {(lastHitObject != null ? lastHitObject.name : "None")}");
        
        // Raycast info
        if (EventSystem.current != null)
        {
            PointerEventData pointerData = new PointerEventData(EventSystem.current)
            {
                position = Input.mousePosition
            };
            
            List<RaycastResult> results = new List<RaycastResult>();
            EventSystem.current.RaycastAll(pointerData, results);
            
            debugInfo.AppendLine($"Raycast hits: {results.Count}");
            
            // Show top 3 hits for brevity
            int displayCount = Mathf.Min(results.Count, 3);
            for (int i = 0; i < displayCount; i++)
            {
                RaycastResult result = results[i];
                debugInfo.AppendLine($"  [{i}] {result.gameObject.name}");
            }
        }
    }
    
    private void OnGUI()
    {
        if (!isActive || !showOnScreen || !isDisplayVisible) return;
        
        // Initialize style if needed
        if (debugStyle == null)
        {
            debugStyle = new GUIStyle(GUI.skin.box);
            debugStyle.normal.textColor = textColor;
            debugStyle.fontSize = 12;
            debugStyle.alignment = TextAnchor.UpperLeft;
            debugStyle.padding = new RectOffset(5, 5, 5, 5);
            debugRect = new Rect(displayPosition.x, displayPosition.y, displaySize.x, displaySize.y);
        }
        
        // Draw background
        GUI.color = backgroundColor;
        GUI.Box(debugRect, "");
        
        // Draw text
        GUI.color = textColor;
        GUI.Label(debugRect, debugInfo.ToString(), debugStyle);
        
        // Reset color
        GUI.color = Color.white;
        
        // Draw Ray
        if (drawRays && lastHitObject != null)
        {
            Debug.DrawRay(Camera.main.ScreenToWorldPoint(new Vector3(Input.mousePosition.x, Input.mousePosition.y, 10)), 
                Vector3.forward * 10, Color.red);
        }
    }
    
    private void LogMessage(string message)
    {
        if (logToConsole)
        {
            Debug.Log($"[UIInputDebugger] {message}");
        }
    }
    
    private string GetObjectPath(GameObject obj)
    {
        if (obj == null) return "null";
        
        StringBuilder path = new StringBuilder(obj.name);
        Transform parent = obj.transform.parent;
        
        while (parent != null)
        {
            path.Insert(0, parent.name + "/");
            parent = parent.parent;
        }
        
        return path.ToString();
    }
} 