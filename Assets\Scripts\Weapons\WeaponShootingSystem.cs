using System.Collections;
using UnityEngine;

/// <summary>
/// <PERSON>les weapon shooting mechanics including different firing modes, accuracy, and recoil
/// </summary>
public class WeaponShootingSystem : MonoBehaviour
{
    [Header("References")]
    [Tooltip("Reference to the player transform for shooting direction")]
    public Transform playerTransform;

    [Tooltip("Reference to the camera for mouse position calculation")]
    public Camera playerCamera;

    [Toolt<PERSON>("Reference to the weapon system for ammo management")]
    public WeaponSystem weaponSystem;

    [Toolt<PERSON>("Reference to the hotbar to get selected weapon")]
    public Hotbar hotbar;

    [Tooltip("Reference to the bullet spawner")]
    public BulletSpawner bulletSpawner;
    
    [<PERSON><PERSON>("Shooting Settings")]
    [Tooltip("Whether the player is currently aiming")]
    public bool isAiming = false;
    
    [Tooltip("Whether the player is currently moving")]
    public bool isMoving = false;
    
    [Header("Accuracy System")]
    [Tooltip("Current accuracy spread angle")]
    [SerializeField] private float currentSpread = 0f;
    
    [Tooltip("Time since last shot (for accuracy recovery)")]
    [SerializeField] private float timeSinceLastShot = 0f;
    
    [<PERSON><PERSON>("Firing State")]
    [Tooltip("Whether weapon is currently firing (for full-auto)")]
    [SerializeField] private bool isFiring = false;
    
    [Tooltip("Time until next shot can be fired")]
    [SerializeField] private float nextFireTime = 0f;
    
    [Tooltip("Whether weapon is in pump delay (shotgun)")]
    [SerializeField] private bool isPumping = false;
    
    // Current weapon reference
    private InventoryItem currentWeapon;
    
    private void Awake()
    {
        // Find references if not assigned
        if (playerTransform == null)
            playerTransform = transform;

        if (playerCamera == null)
            playerCamera = Camera.main;

        if (weaponSystem == null)
            weaponSystem = FindObjectOfType<WeaponSystem>();

        if (hotbar == null)
            hotbar = FindObjectOfType<Hotbar>();

        if (bulletSpawner == null)
            bulletSpawner = FindObjectOfType<BulletSpawner>();
    }
    
    private void Update()
    {
        // Update current weapon
        UpdateCurrentWeapon();
        
        // Handle input
        HandleShootingInput();
        
        // Update accuracy recovery
        UpdateAccuracyRecovery();
        
        // Update firing state
        UpdateFiringState();
    }
    
    /// <summary>
    /// Update the currently selected weapon
    /// </summary>
    private void UpdateCurrentWeapon()
    {
        if (hotbar == null) return;
        
        HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
        InventoryItem newWeapon = null;
        
        if (selectedSlot?.storedItem?.itemData?.itemType == ItemType.Weapon)
        {
            newWeapon = selectedSlot.storedItem;
        }
        
        if (newWeapon != currentWeapon)
        {
            currentWeapon = newWeapon;
            // Reset firing state when weapon changes
            isFiring = false;
            isPumping = false;
            currentSpread = 0f;
        }
    }
    
    /// <summary>
    /// Handle shooting input based on weapon type
    /// </summary>
    private void HandleShootingInput()
    {
        // Update aiming state FIRST
        isAiming = Input.GetMouseButton(1); // Right mouse button

        // Check if we have a weapon
        if (currentWeapon == null) return;

        // Only allow shooting while aiming
        if (!isAiming)
        {
            isFiring = false;
            return;
        }

        // Check if we can shoot (now that aiming is updated)
        if (!CanShoot()) return;

        // Handle different weapon types
        switch (currentWeapon.itemData.weaponType)
        {
            case WeaponType.Pistol:
                HandleSemiAutomatic();
                break;

            case WeaponType.Rifle:
                HandleFullyAutomatic();
                break;

            case WeaponType.Shotgun:
                HandlePumpAction();
                break;

            case WeaponType.Grenade:
                // TODO: Handle grenade throwing
                break;
        }
    }
    
    /// <summary>
    /// Handle semi-automatic firing (pistol)
    /// </summary>
    private void HandleSemiAutomatic()
    {
        if (Input.GetMouseButtonDown(0)) // Left mouse button pressed
        {
            if (isAiming)
            {
                TryFireWeapon();
            }
            else
            {
                Debug.Log("Must aim (hold Mouse 2) to shoot!");
            }
        }
    }
    
    /// <summary>
    /// Handle fully automatic firing (rifle)
    /// </summary>
    private void HandleFullyAutomatic()
    {
        if (Input.GetMouseButton(0)) // Left mouse button held
        {
            if (isAiming)
            {
                isFiring = true;
                if (Time.time >= nextFireTime)
                {
                    TryFireWeapon();
                }
            }
            else
            {
                isFiring = false;
                if (Input.GetMouseButtonDown(0)) // Only show message on first click
                {
                    Debug.Log("Must aim (hold Mouse 2) to shoot!");
                }
            }
        }
        else
        {
            isFiring = false;
        }
    }
    
    /// <summary>
    /// Handle pump-action firing (shotgun)
    /// </summary>
    private void HandlePumpAction()
    {
        if (Input.GetMouseButtonDown(0) && !isPumping) // Left mouse button pressed and not pumping
        {
            if (isAiming)
            {
                TryFireWeapon();
            }
            else
            {
                Debug.Log("Must aim (hold Mouse 2) to shoot!");
            }
        }
    }
    
    /// <summary>
    /// Attempt to fire the current weapon
    /// </summary>
    private void TryFireWeapon()
    {
        if (!CanShoot() || Time.time < nextFireTime) return;

        Debug.Log($"TryFireWeapon: {currentWeapon.GetName()} - Current ammo: {currentWeapon.currentAmmo}/{currentWeapon.itemData.magazineSize}");

        // Check if weapon has ammo
        if (currentWeapon.currentAmmo <= 0)
        {
            Debug.Log($"{currentWeapon.GetName()} is empty! Current ammo: {currentWeapon.currentAmmo}/{currentWeapon.itemData.magazineSize}");
            return;
        }
        
        // Fire the weapon
        bool fired = weaponSystem.FireWeapon(currentWeapon);
        
        if (fired)
        {
            // Calculate firing direction with spread
            Vector3 fireDirection = CalculateFireDirection();
            
            // Perform the actual shot
            PerformShot(fireDirection);
            
            // Update accuracy and timing
            ApplyRecoil();
            SetNextFireTime();
            
            // Handle pump delay for shotguns
            if (currentWeapon.itemData.weaponType == WeaponType.Shotgun)
            {
                StartCoroutine(PumpDelay());
            }
        }
    }
    
    /// <summary>
    /// Calculate fire direction with accuracy spread
    /// </summary>
    private Vector3 CalculateFireDirection()
    {
        // Calculate base direction from player to mouse cursor
        Vector3 baseDirection = CalculateMouseDirection();

        // Calculate total spread
        float totalSpread = CalculateTotalSpread();

        // Apply random spread within the cone
        float randomAngle = Random.Range(-totalSpread, totalSpread);
        float randomRotation = Random.Range(0f, 360f);

        // Create spread rotation
        Quaternion spreadRotation = Quaternion.AngleAxis(randomAngle, Vector3.up) *
                                   Quaternion.AngleAxis(randomRotation, baseDirection);

        return spreadRotation * baseDirection;
    }

    /// <summary>
    /// Calculate direction from player to mouse cursor
    /// </summary>
    private Vector3 CalculateMouseDirection()
    {
        // Get camera reference if not set
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                Debug.LogWarning("WeaponShootingSystem: No camera found! Using player forward direction as fallback.");
                return playerTransform.forward;
            }
        }

        // Convert mouse screen position to world position
        Vector3 mouseWorldPos = playerCamera.ScreenToWorldPoint(Input.mousePosition);
        mouseWorldPos.z = 0f; // Ensure we're working in 2D plane

        // Calculate direction from player to mouse
        Vector3 playerPos = playerTransform.position;
        playerPos.z = 0f; // Ensure we're working in 2D plane

        Vector3 direction = (mouseWorldPos - playerPos).normalized;

        // Convert to 3D direction (assuming bullets travel in XY plane)
        return new Vector3(direction.x, direction.y, 0f);
    }
    
    /// <summary>
    /// Calculate total accuracy spread
    /// </summary>
    private float CalculateTotalSpread()
    {
        float baseSpread = currentWeapon.itemData.baseAccuracy;
        float recoilSpread = currentSpread;
        float movementSpread = isMoving ? baseSpread * currentWeapon.itemData.movementSpreadMultiplier : 0f;
        
        return baseSpread + recoilSpread + movementSpread;
    }
    
    /// <summary>
    /// Perform the actual shot (spawn bullet, effects, etc.)
    /// </summary>
    private void PerformShot(Vector3 direction)
    {
        // Spawn bullet projectile
        if (bulletSpawner != null)
        {
            bulletSpawner.SpawnBullet(currentWeapon.itemData.weaponType, direction, currentWeapon);
        }
        else
        {
            Debug.LogWarning("BulletSpawner not found! Bullets will not be spawned.");
        }

        // TODO: Add additional effects
        // - Spawn muzzle flash effects
        // - Play shooting sounds
        // - Camera shake/recoil

        Debug.Log($"Fired {currentWeapon.GetName()} in direction: {direction} with spread: {CalculateTotalSpread():F2}°");
    }
    
    /// <summary>
    /// Apply recoil to accuracy
    /// </summary>
    private void ApplyRecoil()
    {
        float recoilIncrease = currentWeapon.itemData.maxRecoilSpread * 0.1f; // 10% per shot
        currentSpread = Mathf.Min(currentSpread + recoilIncrease, currentWeapon.itemData.maxRecoilSpread);
        timeSinceLastShot = 0f;
    }
    
    /// <summary>
    /// Set the time for next allowed shot based on fire rate
    /// </summary>
    private void SetNextFireTime()
    {
        float fireInterval = 60f / currentWeapon.itemData.fireRate; // Convert RPM to seconds
        nextFireTime = Time.time + fireInterval;
    }
    
    /// <summary>
    /// Update accuracy recovery over time
    /// </summary>
    private void UpdateAccuracyRecovery()
    {
        if (currentWeapon == null) return;
        
        timeSinceLastShot += Time.deltaTime;
        
        // Recover accuracy when not firing
        if (!isFiring && timeSinceLastShot > 0.1f)
        {
            float recoveryAmount = currentWeapon.itemData.accuracyRecoveryRate * Time.deltaTime;
            currentSpread = Mathf.Max(0f, currentSpread - recoveryAmount);
        }
    }
    
    /// <summary>
    /// Update firing state
    /// </summary>
    private void UpdateFiringState()
    {
        // Stop firing if not holding mouse button (for full-auto)
        if (!Input.GetMouseButton(0))
        {
            isFiring = false;
        }
    }
    
    /// <summary>
    /// Pump delay coroutine for shotguns
    /// </summary>
    private IEnumerator PumpDelay()
    {
        isPumping = true;
        yield return new WaitForSeconds(currentWeapon.itemData.pumpDelay);
        isPumping = false;
    }
    
    /// <summary>
    /// Check if weapon can be fired
    /// </summary>
    private bool CanShoot()
    {
        return currentWeapon != null && 
               isAiming && 
               !weaponSystem.IsReloading() && 
               !isPumping;
    }
    
    /// <summary>
    /// Set movement state (called by player controller)
    /// </summary>
    public void SetMovementState(bool moving)
    {
        isMoving = moving;
    }
    
    /// <summary>
    /// Get current accuracy for UI display
    /// </summary>
    public float GetCurrentAccuracy()
    {
        return currentWeapon != null ? CalculateTotalSpread() : 0f;
    }
}
