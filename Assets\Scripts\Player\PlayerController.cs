using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class PlayerController : MonoBehaviour
{
    public float moveSpeed = 5f;
    public float sprintSpeed = 8f;
    public float acceleration = 10f;
    public float deceleration = 15f;
    public Camera cam;
    public GameObject inventoryUI; // Assign this in the Inspector

    [Tooltip("Reference to the InventoryManager - leave empty to find automatically")]
    public InventoryManager inventoryManager; // Reference to InventoryManager

    private Rigidbody2D rb;
    private Vector2 movement;
    private Vector2 mousePos;
    private bool isSprinting = false;
    private bool isAiming = false;
    private Vector2 inputMovement;
    private Animator animator;
    private bool isInventoryOpen = false;

    void Start()
    {
        try
        {
            // Get required components
            if (rb == null) rb = GetComponent<Rigidbody2D>();
            if (animator == null) animator = GetComponent<Animator>();

            // Double check camera reference
            if (cam == null)
            {
                Debug.LogWarning("Camera reference is missing - attempting to find main camera");
                cam = Camera.main;
                if (cam == null)
                {
                    Debug.LogError("Could not find a main camera. Please assign one in the inspector.");
                }
            }

            // Try to find inventory manager if not set
            if (inventoryManager == null)
            {
                Debug.Log("Looking for InventoryManager on start");
                FindInventoryManager();
            }

            // Make sure inventoryUI is set
            if (inventoryUI == null)
            {
                Debug.LogWarning("Inventory UI reference is missing - attempting to find it");

                // Try to find by tag first (more reliable)
                GameObject uiObject = GameObject.FindGameObjectWithTag("InventoryUI");
                if (uiObject != null)
                {
                    inventoryUI = uiObject;
                    Debug.Log("Found InventoryUI by tag");
                }
                else
                {
                    // Try to find by name
                    uiObject = GameObject.Find("InventoryPanel"); // Adjust this name as needed
                    if (uiObject != null)
                    {
                        inventoryUI = uiObject;
                        Debug.Log("Found InventoryUI by name");
                    }
                    else
                    {
                        Debug.LogError("Could not find inventory UI object in scene. Please assign it in the inspector.");
                    }
                }
            }

            // Hide inventory at start
            if (inventoryUI != null)
            {
                inventoryUI.SetActive(false);
                isInventoryOpen = false;
            }
            else
            {
                Debug.LogError("Inventory UI reference is still missing after attempts to find it.");
            }

            // Ensure cursor is visible and unlocked at the start
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PlayerController.Start: {e.Message}\n{e.StackTrace}");
        }
    }

    private void FindInventoryManager()
    {
        // First check if it's already assigned in inspector
        if (inventoryManager != null)
        {
            Debug.Log("InventoryManager already assigned");
            return;
        }

        // Try to get it from this GameObject
        inventoryManager = GetComponent<InventoryManager>();

        // If still null, try finding it in the scene
        if (inventoryManager == null)
        {
            Debug.Log("InventoryManager not found on player, searching in scene...");
            inventoryManager = FindObjectOfType<InventoryManager>();

            if (inventoryManager != null)
            {
                Debug.Log("Found InventoryManager in scene");
            }
            else
            {
                // Try to find InventoryUI first, which may have a reference to the manager
                InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
                if (inventoryUI != null && inventoryUI.inventoryManager != null)
                {
                    inventoryManager = inventoryUI.inventoryManager;
                    Debug.Log("Found InventoryManager through InventoryUI reference");
                }
                else
                {
                    Debug.LogWarning("InventoryManager not found anywhere in the scene!");
                }
            }
        }
        else
        {
            Debug.Log("InventoryManager found on player");
        }
    }

    void Update()
    {
        try
        {
            // Toggle inventory with TAB
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                ToggleInventory();
            }

            // If the inventory is open, check for clicks outside
            if (isInventoryOpen && Input.GetMouseButtonDown(0))
            {
                // Check if EventSystem is available
                if (EventSystem.current != null && !EventSystem.current.IsPointerOverGameObject())
                {
                    // Deselect the current item when clicking outside any UI
                    DeselectCurrentItem();
                }
            }

            // If the inventory is open, freeze player movement
            if (isInventoryOpen)
            {
                movement = Vector2.zero;
                if (animator != null)
                {
                    animator.SetBool("isMoving", false); // Ensure the player is in the idle state
                }
                return; // Skip the rest of the update logic
            }

            // Normal movement and camera controls
            inputMovement = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical")).normalized;

            if (cam != null)
            {
                mousePos = cam.ScreenToWorldPoint(Input.mousePosition);
            }

            // Check if sprinting is allowed (only when moving forward within a slight angle tolerance)
            isSprinting = Input.GetKey(KeyCode.LeftShift) && IsMovingForward(inputMovement, 0.5f);

            // Prevent aiming while sprinting
            isAiming = !isSprinting && Input.GetMouseButton(1);

            // Smooth speed transition
            float targetSpeed = isSprinting ? sprintSpeed : moveSpeed;
            float speedDiff = targetSpeed - movement.magnitude;
            float speedChange = speedDiff > 0 ? acceleration : deceleration;

            movement = Vector2.MoveTowards(movement, inputMovement * targetSpeed, speedChange * Time.deltaTime);

            // Update Animator parameters
            if (animator != null)
            {
                animator.SetBool("isMoving", inputMovement != Vector2.zero);
                animator.SetBool("isSprinting", isSprinting);
            }

            // Update pickup highlighting to show which item will be picked up
            UpdatePickupHighlighting();

            // Check for item pickup input
            if (Input.GetKeyDown(KeyCode.E)) // Change this to your preferred key
            {
                PickupItem(); // Call the method to pick up the item
            }

            // Check for reload input (R key)
            if (Input.GetKeyDown(KeyCode.R))
            {
                TryReloadCurrentWeapon();
            }

            // Debug key to force cleanup of stuck drag visuals (F9)
            if (Input.GetKeyDown(KeyCode.F9))
            {
                ForceCleanupDragVisuals();
            }

            // Check for weapon firing (left click)
            if (Input.GetMouseButtonDown(0) && !EventSystem.current.IsPointerOverGameObject())
            {
                TryFireCurrentWeapon();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PlayerController.Update: {e.Message}\n{e.StackTrace}");
        }
    }

    void FixedUpdate()
    {
        try
        {
            if (isInventoryOpen) return; // Prevent movement when inventory is open

            // Handle player movement
            if (rb != null)
            {
                rb.MovePosition(rb.position + movement * Time.fixedDeltaTime);

                // Handle player rotation (looking towards mouse)
                Vector2 lookDir = mousePos - rb.position;
                float angle = Mathf.Atan2(lookDir.y, lookDir.x) * Mathf.Rad2Deg - 90f;
                rb.rotation = angle + 180f;
            }
            else
            {
                Debug.LogWarning("Rigidbody2D is null in FixedUpdate");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in FixedUpdate: {e.Message}\n{e.StackTrace}");
        }
    }

    void ToggleInventory()
    {
        try
        {
            isInventoryOpen = !isInventoryOpen;

            // Ensure inventory UI exists before trying to access it
            if (inventoryUI != null)
            {
                inventoryUI.SetActive(isInventoryOpen); // Toggle the visibility of the inventory UI

                // If we're closing the inventory, deselect any selected item
                if (!isInventoryOpen)
                {
                    DeselectCurrentItem();
                }
            }
            else
            {
                Debug.LogWarning("Inventory UI reference is missing. Cannot toggle inventory display.");
            }

            // Always keep the cursor visible and unlocked
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in ToggleInventory: {e.Message}");

            // Ensure cursor is always unlocked even if an error occurs
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
    }

    private void DeselectCurrentItem()
    {
        try
        {
            // Check if the inventory system is properly initialized
            if (inventoryManager == null)
            {
                Debug.LogWarning("Cannot deselect item - inventory manager is null");
                return;
            }

            // Safely get the selected item
            var selectedItem = InventoryItemUIDrag.GetSelectedItem();
            if (selectedItem != null)
            {
                selectedItem.Deselect();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Error in DeselectCurrentItem: {e.Message}");
        }
    }

    // Track the currently highlighted item for pickup
    private InventoryItem currentHighlightedItem = null;

    void PickupItem()
    {
        try
        {
            if (inventoryManager == null)
            {
                FindInventoryManager();

                if (inventoryManager == null)
                {
                    return;
                }
            }

            // Find the closest pickupable item
            InventoryItem closestItem = FindClosestPickupableItem();

            if (closestItem == null)
            {
                return;
            }

            // Try to add the item to inventory
            if (inventoryManager.PickupItem(closestItem))
            {
                // Hide the pickup indicator before deactivating
                closestItem.HidePickupIndicator();
                currentHighlightedItem = null;

                closestItem.gameObject.SetActive(false);

                // Verify inventory consistency after pickup to ensure proper tracking
                inventoryManager.VerifyInventoryConsistency();

                // Force trigger inventory changed event to update all systems
                inventoryManager.TriggerInventoryChanged();
            }

        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PickupItem: {e.Message}\n{e.StackTrace}");
        }
    }

    // Find the closest item that can be picked up
    private InventoryItem FindClosestPickupableItem()
    {
        Collider2D[] hitColliders = Physics2D.OverlapCircleAll(transform.position, 1.5f);
        if (hitColliders == null || hitColliders.Length == 0)
            return null;

        InventoryItem closestItem = null;
        float closestDistance = float.MaxValue;

        foreach (var hitCollider in hitColliders)
        {
            if (hitCollider == null)
                continue;

            InventoryItem item = hitCollider.GetComponent<InventoryItem>();
            if (item == null || item.itemData == null)
                continue;

            float distance = Vector2.Distance(transform.position, hitCollider.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestItem = item;
            }
        }

        return closestItem;
    }

    // Update pickup highlighting - call this regularly to show which item will be picked up
    private void UpdatePickupHighlighting()
    {
        InventoryItem closestItem = FindClosestPickupableItem();

        // If the highlighted item changed
        if (currentHighlightedItem != closestItem)
        {
            // Hide previous highlight
            if (currentHighlightedItem != null)
            {
                currentHighlightedItem.HidePickupIndicator();
            }

            // Show new highlight
            currentHighlightedItem = closestItem;
            if (currentHighlightedItem != null)
            {
                currentHighlightedItem.ShowPickupIndicator();
            }
        }
    }


    public bool IsSprinting()
    {
        return isSprinting;
    }

    public bool IsMoving()
    {
        return inputMovement != Vector2.zero;
    }

    public bool IsAiming()
    {
        return isAiming;
    }

    private bool IsMovingForward(Vector2 movementInput, float tolerance)
    {
        if (rb == null) return false;

        Vector2 forwardDirection = (mousePos - rb.position).normalized;
        float dotProduct = Vector2.Dot(forwardDirection, movementInput);
        return dotProduct > tolerance;
    }

    public bool IsInventoryOpen()
    {
        if (inventoryUI == null)
        {
            Debug.LogError("Inventory system is not initialized.");
            return false;
        }
        return isInventoryOpen;
    }

    public Vector2 GetMovementInput()
    {
        return inputMovement;
    }

    /// <summary>
    /// Try to reload the currently selected weapon in hotbar
    /// </summary>
    private void TryReloadCurrentWeapon()
    {
        // Find the hotbar and get the currently selected weapon
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return;

        HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
        if (selectedSlot == null || selectedSlot.storedItem == null) return;

        // Check if it's a weapon
        if (selectedSlot.storedItem.itemData.itemType != ItemType.Weapon) return;

        // Find weapon system and try to reload
        WeaponSystem weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.TryReloadWeapon(selectedSlot.storedItem);
        }
        else
        {
            Debug.LogError("WeaponSystem not found! Cannot reload weapon.");
        }
    }

    /// <summary>
    /// Try to fire the currently selected weapon in hotbar
    /// </summary>
    private void TryFireCurrentWeapon()
    {
        // Find the hotbar and get the currently selected weapon
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return;

        HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
        if (selectedSlot == null || selectedSlot.storedItem == null) return;

        // Check if it's a weapon
        if (selectedSlot.storedItem.itemData.itemType != ItemType.Weapon) return;

        // Find weapon system and try to fire
        WeaponSystem weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.FireWeapon(selectedSlot.storedItem);
        }
        else
        {
            Debug.LogError("WeaponSystem not found! Cannot fire weapon.");
        }
    }

    /// <summary>
    /// Force cleanup of any stuck drag visuals (debug function)
    /// </summary>
    private void ForceCleanupDragVisuals()
    {
        // Find and cleanup inventory UI
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
        if (inventoryUI != null)
        {
            inventoryUI.ForceCleanupDragVisuals();
        }

        // Also clear any static drag states
        InventoryItemUIDrag.ClearCurrentDraggedItem();
    }

    // Clean up highlighting when the player is destroyed or disabled
    private void OnDisable()
    {
        if (currentHighlightedItem != null)
        {
            currentHighlightedItem.HidePickupIndicator();
            currentHighlightedItem = null;
        }
    }
}
